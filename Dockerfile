# Multi-stage build for CCTV Monitoring System
# Optimized for serverless deployment

# Stage 1: Base image with system dependencies
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies for OpenCV and other libraries
RUN apt-get update && apt-get install -y \
    # OpenCV dependencies
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libgtk-3-0 \
    # Additional system libraries
    libgomp1 \
    libgcc-s1 \
    # Network and utilities
    curl \
    wget \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Stage 2: Python dependencies
FROM base as dependencies

# Create app directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Stage 3: Application
FROM dependencies as application

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/models \
    && mkdir -p /app/uploads \
    && mkdir -p /app/logs \
    && mkdir -p /app/static/uploads

# Download YOLOv5 models if not present (for serverless cold starts)
RUN echo "import torch\nfrom ultralytics import YOLO\nimport os\nmodels = ['yolov5s.pt', 'yolov5m.pt']\nfor model in models:\n    if not os.path.exists(model):\n        print(f'Downloading {model}...')\n        try:\n            yolo = YOLO(model)\n            print(f'✓ {model} downloaded successfully')\n        except Exception as e:\n            print(f'Warning: Could not download {model}: {e}')" > download_models.py && python download_models.py && rm download_models.py

# Set proper permissions
RUN chmod +x start_server.py \
    && chmod 755 /app

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/api/v1/system/health || exit 1

# Default command for regular deployment
CMD ["python", "start_server.py"]

# Stage 4: Serverless optimized
FROM application as serverless

# Switch back to root for serverless optimizations
USER root

# Install additional serverless dependencies
RUN pip install --no-cache-dir \
    gunicorn \
    gevent

# Create serverless entry point
RUN echo '#!/usr/bin/env python3\n\
    import os\n\
    from app import app\n\
    \n\
    if __name__ == "__main__":\n\
    port = int(os.environ.get("PORT", 5000))\n\
    host = os.environ.get("HOST", "0.0.0.0")\n\
    \n\
    # For serverless environments\n\
    if os.environ.get("SERVERLESS", "false").lower() == "true":\n\
    # Use gunicorn for better serverless performance\n\
    import subprocess\n\
    import sys\n\
    cmd = [\n\
    "gunicorn",\n\
    "--bind", f"{host}:{port}",\n\
    "--workers", "1",\n\
    "--worker-class", "gevent",\n\
    "--worker-connections", "1000",\n\
    "--timeout", "120",\n\
    "--keep-alive", "2",\n\
    "--max-requests", "1000",\n\
    "--max-requests-jitter", "100",\n\
    "--preload",\n\
    "app:app"\n\
    ]\n\
    subprocess.run(cmd)\n\
    else:\n\
    # Regular Flask development server\n\
    app.run(debug=False, host=host, port=port, threaded=True)\n\
    ' > /app/serverless_start.py \
    && chmod +x /app/serverless_start.py

# Create gunicorn config for serverless
RUN echo 'import os\n\
    \n\
    # Server socket\n\
    bind = f"0.0.0.0:{os.environ.get(\"PORT\", 5000)}"\n\
    backlog = 2048\n\
    \n\
    # Worker processes\n\
    workers = 1\n\
    worker_class = "gevent"\n\
    worker_connections = 1000\n\
    timeout = 120\n\
    keepalive = 2\n\
    \n\
    # Restart workers\n\
    max_requests = 1000\n\
    max_requests_jitter = 100\n\
    \n\
    # Logging\n\
    accesslog = "-"\n\
    errorlog = "-"\n\
    loglevel = "info"\n\
    \n\
    # Process naming\n\
    proc_name = "cctv-monitoring"\n\
    \n\
    # Server mechanics\n\
    preload_app = True\n\
    daemon = False\n\
    pidfile = "/tmp/gunicorn.pid"\n\
    user = "appuser"\n\
    group = "appuser"\n\
    tmp_upload_dir = None\n\
    \n\
    # SSL (if needed)\n\
    keyfile = None\n\
    certfile = None\n\
    ' > /app/gunicorn.conf.py

# Switch back to appuser
RUN chown -R appuser:appuser /app
USER appuser

# Serverless-specific environment variables
ENV SERVERLESS=true \
    FLASK_ENV=production \
    FLASK_DEBUG=0

# Override CMD for serverless
CMD ["python", "serverless_start.py"]
