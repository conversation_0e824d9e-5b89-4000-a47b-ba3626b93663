# Perbaikan Deteksi Kendaraan - index.py

## <PERSON><PERSON><PERSON> yang Ditem<PERSON>

1. **Confidence threshold terlalu rendah (0.2)**
   - Menyebabkan banyak false positive
   - Banner, papan reklame, dan objek lain terdeteksi sebagai kendaraan

2. **Tidak ada validasi ukuran objek**
   - Objek kecil (banner) bisa terdeteksi sebagai truk
   - Objek dengan proporsi salah tidak difilter

3. **Tidak ada Region of Interest (ROI)**
   - Objek di trotoar ikut terdeteksi
   - Tidak membedakan area jalan dengan area non-jalan

4. **Preprocessing berlebihan**
   - CLAHE dan Gaus<PERSON> blur mengubah karakteristik objek
   - Bisa menyebabkan misklasifikasi

## Perbaikan yang Dilakukan

### 1. Penyesuaian Confidence Threshold
```python
# Sebelum
model.conf = 0.2

# Sesudah
model.conf = 0.3  # Seimbang antara akurasi dan sensitivitas
```

### 2. Validasi Ukuran Objek
Ditambahkan fungsi `validate_vehicle_size()` yang memvalidasi:
- **Area relatif** terhadap frame
- **Aspect ratio** (perbandingan lebar/tinggi)

**Parameter validasi per jenis kendaraan (DIPERLONGGAR):**
- **Mobil**: area 0.05%-40%, aspect ratio 0.8-4.0
- **Motor**: area 0.02%-15%, aspect ratio 0.5-3.0
- **Bus**: area 0.5%-60%, aspect ratio 1.5-5.0
- **Truk**: area 0.3%-50%, aspect ratio 1.0-4.0
- **Sepeda**: area 0.01%-8%, aspect ratio 0.5-2.5

### 3. Region of Interest (ROI) - DIPERLUAS
Ditambahkan fungsi `is_in_road_area()` yang:
- Membatasi deteksi hanya di area jalan
- Mengabaikan objek di trotoar (10% kiri-kanan frame) - LEBIH SEMPIT
- Mengabaikan objek di langit/bangunan (15% atas frame) - LEBIH RENDAH

### 4. Preprocessing Minimal
```python
# Sebelum: CLAHE + Gaussian blur (15 baris kode)
# Sesudah: Preprocessing minimal (3 baris kode)
```

### 5. Logika Deteksi Bertingkat
Sistem deteksi yang lebih cerdas:
- **Confidence tinggi (>0.5)**: Langsung terima tanpa validasi tambahan
- **Confidence sedang-tinggi (0.4-0.5)**: Perlu validasi ukuran ATAU ROI
- **Confidence sedang-rendah (0.3-0.4)**: Perlu validasi ukuran DAN ROI
- **Confidence rendah (<0.3)**: Ditolak

### 6. Fitur Debugging Lengkap
- **Visualisasi ROI**: Garis kuning menunjukkan area deteksi
- **Info ukuran objek**: Menampilkan persentase area objek
- **Debug missed detection**: Menampilkan kendaraan yang tidak terdeteksi
- **Kontrol real-time**:
  - `+` untuk menaikkan confidence
  - `-` untuk menurunkan confidence
  - `r` untuk reset counter
  - `d` untuk debug detail ke console

## Cara Menggunakan

1. **Jalankan program**:
   ```bash
   python index.py
   ```

2. **Kontrol keyboard**:
   - `q`: Keluar dari program
   - `r`: Reset counter kendaraan
   - `+` atau `=`: Naikkan confidence threshold
   - `-`: Turunkan confidence threshold
   - `d`: Debug detail - tampilkan semua deteksi ke console

3. **Monitoring**:
   - Garis kuning menunjukkan zona deteksi
   - Label menampilkan jenis kendaraan, confidence, dan ukuran area
   - Info real-time di pojok kiri atas
   - **MISSED detection** ditampilkan dalam teks merah
   - Tekan `d` untuk debug detail ke console

## Hasil yang Diharapkan

✅ **Mengurangi false positive**:
- Banner tidak lagi terdeteksi sebagai truk
- Objek di trotoar diabaikan

✅ **Meningkatkan akurasi klasifikasi**:
- Mobil tidak salah diklasifikasi sebagai bus
- Validasi ukuran mencegah kesalahan deteksi

✅ **Kontrol yang lebih baik**:
- Dapat menyesuaikan sensitivity secara real-time
- Visualisasi zona deteksi untuk debugging

## Catatan Penting

- **ROI dapat disesuaikan** berdasarkan sudut kamera CCTV
- **Parameter validasi ukuran** dapat di-fine-tune sesuai kondisi lapangan
- **Confidence threshold** dapat diatur secara real-time menggunakan keyboard
