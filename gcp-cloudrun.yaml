apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: cctv-monitoring
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # CPU and memory configuration
        run.googleapis.com/cpu: "1"
        run.googleapis.com/memory: "2Gi"
        
        # Scaling configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Timeout configuration
        run.googleapis.com/timeout: "300"
        
        # Concurrency configuration
        run.googleapis.com/execution-environment: gen2
        
    spec:
      containerConcurrency: 10
      timeoutSeconds: 300
      containers:
      - name: cctv-monitoring
        image: gcr.io/PROJECT-ID/cctv-monitoring:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: SERVERLESS
          value: "true"
        - name: PORT
          value: "8080"
        - name: FLASK_ENV
          value: "production"
        - name: FLASK_DEBUG
          value: "0"
        resources:
          limits:
            cpu: "1"
            memory: "2Gi"
          requests:
            cpu: "0.5"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /api/v1/system/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/system/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
  traffic:
  - percent: 100
    latestRevision: true
