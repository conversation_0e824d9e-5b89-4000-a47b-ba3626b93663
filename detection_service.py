import cv2
import numpy as np
import math
from collections import defaultdict
from datetime import datetime, timedelta
import threading
import time

# Class IDs untuk semua jenis kend<PERSON> di COCO dataset
VEHICLE_CLASSES = [
    2,   # car
    3,   # motorcycle
    5,   # bus
    7,   # truck
    1,   # bicycle (sepeda juga kendaraan)
]

def validate_vehicle_size(x1, y1, x2, y2, class_id, frame_width, frame_height):
    """Validasi ukuran objek untuk mengurangi false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height

    # Persentase area relatif terhadap frame
    relative_area = area / (frame_width * frame_height)

    # Rasio aspek (width/height)
    aspect_ratio = width / height if height > 0 else 0

    # Validasi berdasarkan jenis kendaraan (parameter lebih fleksibel)
    if class_id == 2:  # car
        # Mobil: area 0.0005-0.4, aspect ratio 0.8-4.0 (lebih fleksibel)
        return 0.0005 <= relative_area <= 0.4 and 0.8 <= aspect_ratio <= 4.0
    elif class_id == 3:  # motorcycle
        # Motor: area 0.00005-0.3, aspect ratio 0.2-5.0 (sangat fleksibel untuk motor berjajar dan kecil)
        return 0.00005 <= relative_area <= 0.3 and 0.2 <= aspect_ratio <= 5.0
    elif class_id == 5:  # bus
        # Bus: area 0.005-0.6, aspect ratio 1.5-5.0 (lebih fleksibel)
        return 0.005 <= relative_area <= 0.6 and 1.5 <= aspect_ratio <= 5.0
    elif class_id == 7:  # truck
        # Truk: area 0.003-0.5, aspect ratio 1.0-4.0 (lebih fleksibel)
        return 0.003 <= relative_area <= 0.5 and 1.0 <= aspect_ratio <= 4.0
    elif class_id == 1:  # bicycle
        # Sepeda: area 0.0001-0.08, aspect ratio 0.5-2.5 (lebih fleksibel)
        return 0.0001 <= relative_area <= 0.08 and 0.5 <= aspect_ratio <= 2.5

    return True  # Default untuk kelas lain

def validate_low_confidence_motorcycle(x1, y1, x2, y2, conf, frame_width, frame_height):
    """Validasi ketat untuk motor dengan confidence rendah untuk mengurangi false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height
    relative_area = area / (frame_width * frame_height)
    aspect_ratio = width / height if height > 0 else 0
    
    # Untuk motor dengan confidence sangat rendah, validasi lebih ketat
    if conf < 0.15:
        # Harus memenuhi kriteria ukuran yang lebih ketat
        size_valid = (0.0001 <= relative_area <= 0.1 and 
                     0.4 <= aspect_ratio <= 3.5)
        
        # Harus memiliki ukuran minimum yang masuk akal untuk motor
        min_size_valid = width >= 15 and height >= 15
        
        # Tidak boleh terlalu besar (kemungkinan false positive)
        max_size_valid = relative_area <= 0.05
        
        return size_valid and min_size_valid and max_size_valid
    
    # Untuk confidence sedang, validasi normal
    return True

def is_likely_false_positive(x1, y1, x2, y2, conf, class_id, frame_width, frame_height):
    """Deteksi objek yang kemungkinan false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height
    relative_area = area / (frame_width * frame_height)
    aspect_ratio = width / height if height > 0 else 0
    center_y = (y1 + y2) / 2
    
    # Khusus untuk motor dengan confidence rendah
    if class_id == 3 and conf < 0.2:
        false_positive_score = 0
        
        # 1. Objek terlalu kecil untuk menjadi motor
        if relative_area < 0.00005:  # Sangat kecil
            false_positive_score += 2
            
        # 2. Aspect ratio yang sangat tidak wajar untuk motor
        if aspect_ratio < 0.15 or aspect_ratio > 6.0:
            false_positive_score += 2
            
        # 3. Objek di area yang sangat tidak mungkin (pojok atas frame)
        if center_y < frame_height * 0.05:  # 5% teratas
            false_positive_score += 1
            
        # 4. Objek terlalu tipis (kemungkinan garis atau bayangan)
        if width < 10 or height < 10:
            false_positive_score += 2
            
        # 5. Objek dengan ukuran yang sangat tidak proporsional
        if width > height * 8 or height > width * 8:  # Sangat tidak proporsional
            false_positive_score += 1
            
        # 6. Area terlalu besar untuk confidence yang rendah
        if relative_area > 0.02 and conf < 0.1:  # Besar tapi confidence rendah
            false_positive_score += 1
            
        # Jika skor false positive tinggi, kemungkinan bukan motor
        return false_positive_score >= 3
    
    return False

def is_in_road_area(x1, y1, x2, y2, frame_width, frame_height):
    """Validasi apakah objek berada di area jalan (bukan trotoar)"""
    center_x = (x1 + x2) / 2
    center_y = (y1 + y2) / 2

    # Definisi area jalan (sangat luas untuk motor di pinggir)
    # Area yang sangat fleksibel untuk motor yang parkir/berhenti di pinggir
    road_left = frame_width * 0.05   # 5% dari kiri (sangat sempit untuk motor pinggir)
    road_right = frame_width * 0.95  # 5% dari kanan (sangat sempit untuk motor pinggir)
    road_top = frame_height * 0.1    # 10% dari atas (lebih rendah lagi)

    # Objek harus berada di area jalan
    return road_left <= center_x <= road_right and center_y >= road_top

class VehicleTracker:
    """Sistem tracking untuk menghitung kendaraan unik dengan auto-reset harian"""

    def __init__(self):
        self.tracked_vehicles = {}  # ID -> vehicle info
        self.next_id = 1
        self.max_disappeared = 20  # Frame maksimum sebelum dianggap hilang (dikurangi)
        self.max_distance = 80     # Jarak maksimum untuk menganggap objek sama (dikurangi)
        self.min_frames_to_count = 8  # Minimum frame untuk dihitung sebagai kendaraan valid (dinaikkan)

        # Statistik
        self.unique_counts = defaultdict(int)  # Hitung per jenis kendaraan
        self.total_unique = 0

        # Auto-reset functionality
        self.last_reset_date = datetime.now().date()
        self.reset_hour = 0  # Reset at midnight (00:00)
        self.reset_minute = 0
        self.auto_reset_enabled = True
        self.reset_lock = threading.Lock()  # Thread safety untuk reset

        # Start auto-reset checker thread
        self.reset_thread = threading.Thread(target=self._auto_reset_checker, daemon=True)
        self.reset_thread.start()

        print(f"🕛 Auto-reset enabled: Daily at {self.reset_hour:02d}:{self.reset_minute:02d}")
        
    def calculate_distance(self, center1, center2):
        """Hitung jarak Euclidean antara dua titik"""
        return math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
    
    def calculate_iou(self, box1, box2):
        """Hitung Intersection over Union (IoU) antara dua bounding box"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # Hitung area intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0

    def update(self, detections, frame_count):
        """Update tracking dengan deteksi baru"""
        current_detections = []

        # Konversi deteksi ke format yang mudah diproses
        for detection in detections:
            x1, y1, x2, y2, conf, class_id = detection
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            current_detections.append({
                'bbox': (x1, y1, x2, y2),
                'center': (center_x, center_y),
                'class_id': int(class_id),
                'confidence': conf,
                'matched': False
            })

        # Update existing vehicles
        for vehicle_id in list(self.tracked_vehicles.keys()):
            vehicle = self.tracked_vehicles[vehicle_id]
            vehicle['disappeared'] += 1
            vehicle['matched'] = False

        # Match deteksi dengan kendaraan yang sudah ada
        for detection in current_detections:
            best_match_id = None
            best_score = 0

            for vehicle_id, vehicle in self.tracked_vehicles.items():
                if vehicle['class_id'] != detection['class_id']:
                    continue

                # Hitung jarak center
                distance = self.calculate_distance(detection['center'], vehicle['center'])

                # Hitung IoU
                iou = self.calculate_iou(detection['bbox'], vehicle['bbox'])

                # Kriteria matching yang lebih ketat
                if distance < self.max_distance:
                    # Hitung similarity berdasarkan ukuran bounding box
                    det_width = detection['bbox'][2] - detection['bbox'][0]
                    det_height = detection['bbox'][3] - detection['bbox'][1]
                    veh_width = vehicle['bbox'][2] - vehicle['bbox'][0]
                    veh_height = vehicle['bbox'][3] - vehicle['bbox'][1]

                    # Similarity ukuran (0-1, 1 = sama persis)
                    width_ratio = min(det_width, veh_width) / max(det_width, veh_width)
                    height_ratio = min(det_height, veh_height) / max(det_height, veh_height)
                    size_similarity = (width_ratio + height_ratio) / 2

                    # Score gabungan: IoU (40%) + Distance (30%) + Size (30%)
                    distance_score = 1 - (distance / self.max_distance)
                    combined_score = (iou * 0.4 + distance_score * 0.3 + size_similarity * 0.3)

                    # Threshold lebih tinggi untuk mengurangi false match
                    if combined_score > best_score and combined_score > 0.5:
                        best_match_id = vehicle_id
                        best_score = combined_score

            # Jika ada match yang bagus dengan threshold tinggi
            if best_match_id is not None and best_score > 0.5:
                vehicle = self.tracked_vehicles[best_match_id]

                # Update informasi kendaraan
                vehicle['bbox'] = detection['bbox']
                vehicle['center'] = detection['center']
                vehicle['confidence'] = detection['confidence']
                vehicle['disappeared'] = 0
                vehicle['frames_seen'] += 1
                vehicle['last_seen'] = frame_count
                vehicle['matched'] = True
                detection['matched'] = True

                # Jika sudah cukup lama terlihat dan belum dihitung, hitung sebagai unik
                if (vehicle['frames_seen'] >= self.min_frames_to_count and
                    not vehicle['counted_as_unique']):
                    vehicle['counted_as_unique'] = True
                    self.unique_counts[vehicle['class_id']] += 1
                    self.total_unique += 1

                    # Send webhook notification for unique vehicle counted
                    self._send_unique_vehicle_webhook(vehicle_id, vehicle)

        # Tambah kendaraan baru untuk deteksi yang tidak match
        for detection in current_detections:
            if not detection['matched']:
                # Cek sekali lagi apakah ada kendaraan yang sangat dekat
                too_close = False
                for vehicle_id, vehicle in self.tracked_vehicles.items():
                    if vehicle['class_id'] == detection['class_id']:
                        distance = self.calculate_distance(detection['center'], vehicle['center'])
                        if distance < 30:  # Sangat dekat, kemungkinan duplikat
                            too_close = True
                            break

                # Hanya buat ID baru jika tidak terlalu dekat dengan yang ada
                if not too_close:
                    self.tracked_vehicles[self.next_id] = {
                        'bbox': detection['bbox'],
                        'center': detection['center'],
                        'class_id': detection['class_id'],
                        'confidence': detection['confidence'],
                        'first_seen': frame_count,
                        'last_seen': frame_count,
                        'frames_seen': 1,
                        'disappeared': 0,
                        'counted_as_unique': False,
                        'matched': True
                    }
                    self.next_id += 1

        # Hapus kendaraan yang sudah lama hilang
        to_remove = []
        for vehicle_id, vehicle in self.tracked_vehicles.items():
            if vehicle['disappeared'] > self.max_disappeared:
                to_remove.append(vehicle_id)

        for vehicle_id in to_remove:
            del self.tracked_vehicles[vehicle_id]

        # Deteksi dan gabungkan ID duplikat
        self._merge_duplicate_vehicles()

    def _merge_duplicate_vehicles(self):
        """Deteksi dan gabungkan kendaraan dengan ID duplikat"""
        vehicles_to_merge = []
        processed_ids = set()

        # Cari pasangan kendaraan yang kemungkinan duplikat
        for id1, vehicle1 in self.tracked_vehicles.items():
            if id1 in processed_ids:
                continue

            for id2, vehicle2 in self.tracked_vehicles.items():
                if id1 >= id2 or id2 in processed_ids:
                    continue

                # Hanya cek kendaraan dengan class yang sama
                if vehicle1['class_id'] != vehicle2['class_id']:
                    continue

                # Hitung jarak dan similarity
                distance = self.calculate_distance(vehicle1['center'], vehicle2['center'])
                iou = self.calculate_iou(vehicle1['bbox'], vehicle2['bbox'])

                # Jika sangat dekat dan mirip, kemungkinan duplikat
                if distance < 50 and iou > 0.3:
                    # Cek apakah keduanya aktif (baru terlihat)
                    if vehicle1['disappeared'] <= 2 and vehicle2['disappeared'] <= 2:
                        vehicles_to_merge.append((id1, id2))
                        processed_ids.add(id1)
                        processed_ids.add(id2)
                        break

        # Gabungkan kendaraan duplikat
        for id1, id2 in vehicles_to_merge:
            if id1 in self.tracked_vehicles and id2 in self.tracked_vehicles:
                vehicle1 = self.tracked_vehicles[id1]
                vehicle2 = self.tracked_vehicles[id2]

                # Pilih kendaraan yang lebih lama (ID lebih kecil) sebagai master
                master_id = min(id1, id2)
                slave_id = max(id1, id2)

                master = self.tracked_vehicles[master_id]
                slave = self.tracked_vehicles[slave_id]

                # Update master dengan informasi terbaru
                master['frames_seen'] += slave['frames_seen']
                master['bbox'] = slave['bbox']  # Gunakan posisi terbaru
                master['center'] = slave['center']
                master['confidence'] = max(master['confidence'], slave['confidence'])

                # Jika slave sudah dihitung sebagai unik, kurangi counter
                if slave['counted_as_unique'] and not master['counted_as_unique']:
                    # Transfer status unique ke master
                    master['counted_as_unique'] = True
                elif slave['counted_as_unique'] and master['counted_as_unique']:
                    # Kedua sudah dihitung, kurangi duplikat
                    self.unique_counts[slave['class_id']] -= 1
                    self.total_unique -= 1

                # Hapus slave
                del self.tracked_vehicles[slave_id]

    def get_active_vehicles(self):
        """Dapatkan kendaraan yang sedang aktif (terlihat)"""
        active = []
        for vehicle_id, vehicle in self.tracked_vehicles.items():
            if vehicle['disappeared'] == 0:  # Baru saja terlihat
                active.append((vehicle_id, vehicle))
        return active

    def get_statistics(self):
        """Dapatkan statistik tracking"""
        vehicle_names = {1: "Sepeda", 2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk"}

        stats = {
            'total_unique': self.total_unique,
            'by_type': {},
            'currently_tracked': len([v for v in self.tracked_vehicles.values() if v['disappeared'] < 5])
        }

        for class_id, count in self.unique_counts.items():
            vehicle_name = vehicle_names.get(class_id, f"Kendaraan_{class_id}")
            stats['by_type'][vehicle_name] = count

        return stats

    def reset_counts(self):
        """Reset semua hitungan"""
        self.unique_counts.clear()
        self.total_unique = 0
        self.tracked_vehicles.clear()
        self.next_id = 1

    def cleanup_stale_vehicles(self, frame_count):
        """Bersihkan kendaraan yang tidak aktif secara berkala"""
        if frame_count % 50 == 0:  # Setiap 50 frame
            to_remove = []
            for vehicle_id, vehicle in self.tracked_vehicles.items():
                # Hapus kendaraan yang hilang terlalu lama atau tidak pernah dihitung
                if (vehicle['disappeared'] > 10 or
                    (frame_count - vehicle['first_seen'] > 100 and not vehicle['counted_as_unique'])):
                    to_remove.append(vehicle_id)

            for vehicle_id in to_remove:
                del self.tracked_vehicles[vehicle_id]

    def _send_unique_vehicle_webhook(self, vehicle_id, vehicle_info):
        """Send webhook notification for unique vehicle counted"""
        try:
            # Import here to avoid circular imports
            from api import send_webhook_notification

            vehicle_names = {1: "Sepeda", 2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk"}
            vehicle_type = vehicle_names.get(vehicle_info['class_id'], f"Kendaraan_{vehicle_info['class_id']}")

            webhook_data = {
                'vehicle_id': vehicle_id,
                'vehicle_type': vehicle_type,
                'class_id': vehicle_info['class_id'],
                'confidence': float(vehicle_info['confidence']),
                'frames_seen': vehicle_info['frames_seen'],
                'total_unique_count': self.total_unique,
                'bbox': {
                    'x1': float(vehicle_info['bbox'][0]),
                    'y1': float(vehicle_info['bbox'][1]),
                    'x2': float(vehicle_info['bbox'][2]),
                    'y2': float(vehicle_info['bbox'][3])
                },
                'center': {
                    'x': float(vehicle_info['center'][0]),
                    'y': float(vehicle_info['center'][1])
                }
            }

            send_webhook_notification('unique_vehicle_counted', webhook_data)

        except Exception as e:
            print(f"Error sending unique vehicle webhook: {e}")

    def send_vehicle_detection_webhook(self, detections, stream_id=None):
        """Send webhook notification for vehicle detections"""
        try:
            # Import here to avoid circular imports
            from api import send_webhook_notification

            vehicle_names = {1: "Sepeda", 2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk"}

            for detection in detections:
                x1, y1, x2, y2, conf, class_id = detection
                vehicle_type = vehicle_names.get(int(class_id), f"Kendaraan_{int(class_id)}")

                webhook_data = {
                    'stream_id': stream_id,
                    'vehicle_type': vehicle_type,
                    'class_id': int(class_id),
                    'confidence': float(conf),
                    'bbox': {
                        'x1': float(x1),
                        'y1': float(y1),
                        'x2': float(x2),
                        'y2': float(y2)
                    },
                    'center': {
                        'x': float((x1 + x2) / 2),
                        'y': float((y1 + y2) / 2)
                    }
                }

                send_webhook_notification('vehicle_detected', webhook_data)

        except Exception as e:
            print(f"Error sending vehicle detection webhook: {e}")

    def _auto_reset_checker(self):
        """Background thread untuk mengecek dan melakukan auto-reset harian"""
        while True:
            try:
                current_time = datetime.now()
                current_date = current_time.date()

                # Cek apakah sudah waktunya reset (jam 00:00)
                if (current_date > self.last_reset_date and
                    current_time.hour == self.reset_hour and
                    current_time.minute == self.reset_minute and
                    self.auto_reset_enabled):

                    self._perform_daily_reset()

                # Sleep 30 detik sebelum cek lagi
                time.sleep(30)

            except Exception as e:
                print(f"Error in auto-reset checker: {e}")
                time.sleep(60)  # Sleep lebih lama jika ada error

    def _perform_daily_reset(self):
        """Melakukan reset harian pada counting"""
        with self.reset_lock:
            try:
                # Simpan statistik sebelum reset untuk logging
                old_stats = {
                    'total_unique': self.total_unique,
                    'by_type': dict(self.unique_counts),
                    'date': self.last_reset_date
                }

                # Reset semua counter
                self.unique_counts = defaultdict(int)
                self.total_unique = 0
                self.tracked_vehicles = {}  # Clear tracked vehicles
                self.next_id = 1

                # Update tanggal reset
                self.last_reset_date = datetime.now().date()

                # Log reset activity
                current_time = datetime.now()
                print(f"\n🕛 DAILY RESET PERFORMED at {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"📊 Previous day statistics:")
                print(f"   - Total unique vehicles: {old_stats['total_unique']}")
                for class_id, count in old_stats['by_type'].items():
                    vehicle_names = {1: "Sepeda", 2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk"}
                    vehicle_name = vehicle_names.get(class_id, f"Vehicle_{class_id}")
                    print(f"   - {vehicle_name}: {count}")
                print(f"🔄 Counters reset for new day: {self.last_reset_date}")

                # Send webhook notification for daily reset
                try:
                    from api import send_webhook_notification
                    webhook_data = {
                        'reset_time': current_time.isoformat(),
                        'previous_day_stats': old_stats,
                        'new_date': self.last_reset_date.isoformat()
                    }
                    send_webhook_notification('daily_reset', webhook_data)
                except Exception as e:
                    print(f"Warning: Could not send daily reset webhook: {e}")

            except Exception as e:
                print(f"Error performing daily reset: {e}")

    def manual_reset(self):
        """Manual reset untuk testing atau keperluan khusus"""
        with self.reset_lock:
            old_total = self.total_unique
            old_counts = dict(self.unique_counts)

            self.unique_counts = defaultdict(int)
            self.total_unique = 0
            self.tracked_vehicles = {}
            self.next_id = 1

            print(f"🔄 Manual reset performed")
            print(f"   Previous total: {old_total}")
            print(f"   Previous counts: {old_counts}")
            print(f"   All counters reset to 0")

    def set_reset_time(self, hour=0, minute=0):
        """Set waktu reset harian (default: 00:00)"""
        if 0 <= hour <= 23 and 0 <= minute <= 59:
            self.reset_hour = hour
            self.reset_minute = minute
            print(f"🕛 Reset time updated to {hour:02d}:{minute:02d}")
        else:
            print("❌ Invalid time format. Hour: 0-23, Minute: 0-59")

    def enable_auto_reset(self, enabled=True):
        """Enable/disable auto-reset functionality"""
        self.auto_reset_enabled = enabled
        status = "enabled" if enabled else "disabled"
        print(f"🕛 Auto-reset {status}")

    def get_reset_info(self):
        """Get informasi tentang auto-reset"""
        return {
            'auto_reset_enabled': self.auto_reset_enabled,
            'reset_time': f"{self.reset_hour:02d}:{self.reset_minute:02d}",
            'last_reset_date': self.last_reset_date.isoformat(),
            'next_reset': f"{self.last_reset_date + timedelta(days=1)} {self.reset_hour:02d}:{self.reset_minute:02d}"
        }

def process_detections(detections, frame, vehicle_classes, strict_filtering=True):
    """Process YOLO detections and return valid detections with annotated frame"""
    valid_detections = []
    frame_height, frame_width = frame.shape[:2]
    
    # Vehicle type names for display
    vehicle_names = {2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk", 1: "Sepeda"}
    
    for detection in detections:
        x1, y1, x2, y2, conf, class_id = detection
        class_id = int(class_id)
        
        # Filter deteksi kendaraan dengan validasi yang lebih fleksibel
        # Cek confidence threshold yang disesuaikan (sangat rendah untuk motor)
        if class_id == 3:  # Motor
            confidence_ok = conf > 0.05   # Threshold sangat rendah untuk motor (diperluas)
        else:
            confidence_ok = conf > 0.3   # Threshold normal untuk kendaraan lain

        # Cek apakah kelas kendaraan
        is_vehicle = class_id in vehicle_classes

        # Validasi ukuran (opsional untuk kendaraan kecil/jauh)
        size_ok = validate_vehicle_size(x1, y1, x2, y2, class_id, frame_width, frame_height)

        # Validasi ROI (opsional untuk kendaraan di pinggir)
        roi_ok = is_in_road_area(x1, y1, x2, y2, frame_width, frame_height)

        # Cek apakah kemungkinan false positive (hanya jika strict filtering aktif)
        is_false_positive = strict_filtering and is_likely_false_positive(x1, y1, x2, y2, conf, class_id, frame_width, frame_height)

        # Logika deteksi yang lebih fleksibel, khusus untuk motor:
        # - Confidence tinggi (>0.5): langsung terima tanpa validasi lain
        # - Confidence sedang (0.3-0.5): perlu validasi ukuran ATAU ROI
        # - Confidence rendah (<0.3): tolak, kecuali motor dengan validasi khusus
        if confidence_ok and is_vehicle and not is_false_positive:  # Tambahkan filter false positive
            if conf > 0.5:  # Confidence tinggi, langsung terima
                should_detect = True
            elif conf > 0.4:  # Confidence sedang-tinggi, perlu salah satu validasi
                should_detect = size_ok or roi_ok
            elif class_id == 3:  # Motor dengan confidence sedang-rendah, validasi khusus
                if conf < 0.15:  # Motor confidence sangat rendah, validasi ketat
                    low_conf_valid = validate_low_confidence_motorcycle(x1, y1, x2, y2, conf, frame_width, frame_height)
                    should_detect = low_conf_valid and (size_ok or roi_ok)
                else:  # Motor confidence sedang-rendah, lebih toleran
                    should_detect = size_ok or roi_ok
            else:  # Kendaraan lain dengan confidence sedang-rendah, perlu kedua validasi
                should_detect = size_ok and roi_ok
        else:
            should_detect = False

        if should_detect:
            # Add to valid detections
            valid_detections.append([x1, y1, x2, y2, conf, class_id])
            
            # Draw bounding box and label
            # Tentukan warna berdasarkan jenis kendaraan
            if class_id == 2:  # car
                color = (0, 255, 0)  # Hijau
                vehicle_type = "Mobil"
            elif class_id == 3:  # motorcycle
                # Warna berbeda untuk motor berdasarkan confidence dan kondisi
                if conf > 0.4:
                    color = (0, 0, 255)  # Merah untuk motor confidence tinggi
                    vehicle_type = "Motor"
                elif conf > 0.3:
                    color = (0, 100, 255)  # Orange untuk motor berjajar
                    vehicle_type = "Motor*"
                elif conf > 0.2:
                    color = (255, 0, 255)  # Magenta untuk motor berdampingan
                    vehicle_type = "Motor="  # Tanda = untuk motor berdampingan
                elif conf > 0.1:
                    color = (0, 255, 255)  # Kuning untuk motor tertutup
                    vehicle_type = "Motor#"  # Tanda # untuk motor tertutup
                else:
                    color = (255, 255, 0)  # Cyan untuk motor di pinggir
                    vehicle_type = "Motor~"  # Tanda ~ untuk motor pinggir
            elif class_id == 5:  # bus
                color = (255, 0, 0)  # Biru
                vehicle_type = "Bus"
            elif class_id == 7:  # truck
                color = (0, 255, 255)  # Kuning
                vehicle_type = "Truk"
            elif class_id == 1:  # bicycle
                color = (255, 0, 255)  # Magenta
                vehicle_type = "Sepeda"
            else:
                color = (128, 128, 128)  # Abu-abu
                vehicle_type = "Kendaraan"

            # Gambar bounding box dengan ketebalan yang lebih tebal
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 3)

            # Label dengan informasi lebih detail termasuk ukuran untuk debugging
            width = x2 - x1
            height = y2 - y1
            area_percent = (width * height) / (frame_width * frame_height) * 100
            label = f"{vehicle_type}: {conf:.2f} ({area_percent:.1f}%)"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

            # Background untuk label
            cv2.rectangle(frame, (int(x1), int(y1)-label_size[1]-10),
                         (int(x1)+label_size[0], int(y1)), color, -1)

            # Text label
            cv2.putText(frame, label, (int(x1), int(y1)-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return valid_detections, frame
