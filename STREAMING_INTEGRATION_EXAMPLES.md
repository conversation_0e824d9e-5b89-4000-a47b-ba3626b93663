# CCTV Video Streaming - Integration Examples

## 🎯 Ringkasan Fitur Video Streaming

Sistem CCTV Monitoring sekarang mendukung **live video streaming** melalui API yang memungkinkan project lain untuk:

✅ **Melihat live stream** dengan overlay deteksi real-time  
✅ **Mengakses frame saat ini** dalam format base64  
✅ **Share stream** secara public atau private  
✅ **Kontrol akses** dengan API key authentication  
✅ **Multi-platform support** (Python, JavaScript, PHP, HTML)

## 🚀 Quick Start - Cara Menggunakan

### 1. **Start CCTV System**
```bash
python app.py
```

### 2. **Buat Stream Baru**
```bash
curl -X POST http://localhost:5000/api/v1/streams \
  -H "X-API-Key: cctv_admin_[your-key]" \
  -H "Content-Type: application/json" \
  -d '{
    "stream_url": "http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8",
    "name": "CCTV Malang"
  }'
```

Response akan memberikan `stream_id` yang digunakan untuk akses video.

### 3. **Akses Live Video Stream**

#### **Langsung di Browser:**
```
http://localhost:5000/api/v1/streams/{stream_id}/video_feed?api_key=your-key
```

#### **Embed di HTML:**
```html
<img src="http://localhost:5000/api/v1/streams/{stream_id}/video_feed?api_key=your-key" 
     style="width: 100%;" alt="Live CCTV">
```

## 💻 Contoh Implementasi

### **1. Python - OpenCV Display**

```python
#!/usr/bin/env python3
import cv2
import requests
import base64
import numpy as np
from io import BytesIO
from PIL import Image

class SimpleCCTVClient:
    def __init__(self, api_url, api_key):
        self.api_url = api_url
        self.api_key = api_key
    
    def view_live_stream(self, stream_id):
        """Display live stream using OpenCV"""
        stream_url = f"{self.api_url}/streams/{stream_id}/video_feed?api_key={self.api_key}"
        
        print(f"🎥 Opening stream: {stream_url}")
        print("Press 'q' to quit, 's' to save screenshot")
        
        cap = cv2.VideoCapture(stream_url)
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("Failed to read frame")
                break
            
            cv2.imshow('CCTV Live Stream', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                cv2.imwrite(f'screenshot_{stream_id}.jpg', frame)
                print("Screenshot saved!")
        
        cap.release()
        cv2.destroyAllWindows()
    
    def get_current_frame(self, stream_id):
        """Get current frame with detection info"""
        url = f"{self.api_url}/streams/{stream_id}/frame"
        headers = {'X-API-Key': self.api_key}
        
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                frame_data = data['data']
                print(f"Frame {frame_data['frame_count']}: {frame_data['detection_count']} detections")
                
                # Decode base64 image
                image_data = base64.b64decode(frame_data['frame_base64'])
                image = Image.open(BytesIO(image_data))
                frame = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
                
                return frame, frame_data
        return None, None

# Usage
if __name__ == "__main__":
    client = SimpleCCTVClient(
        "http://localhost:5000/api/v1",
        "cctv_admin_[your-key]"
    )
    
    stream_id = "your-stream-id"
    
    # Option 1: Live stream
    client.view_live_stream(stream_id)
    
    # Option 2: Single frame
    # frame, data = client.get_current_frame(stream_id)
    # if frame is not None:
    #     cv2.imshow('Current Frame', frame)
    #     cv2.waitKey(0)
```

### **2. JavaScript - Web Integration**

```html
<!DOCTYPE html>
<html>
<head>
    <title>CCTV Live Stream</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .stream-container { text-align: center; margin: 20px 0; }
        .stream-video { max-width: 100%; border: 2px solid #333; }
        .controls { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🎥 CCTV Live Monitoring</h1>
    
    <div class="stream-container">
        <img id="liveStream" class="stream-video" alt="CCTV Stream">
    </div>
    
    <div class="controls">
        <button class="btn" onclick="startStream()">▶️ Start Stream</button>
        <button class="btn" onclick="stopStream()">⏹️ Stop Stream</button>
        <button class="btn" onclick="takeScreenshot()">📸 Screenshot</button>
        <button class="btn" onclick="refreshStream()">🔄 Refresh</button>
    </div>
    
    <div class="stats" id="stats">
        <h3>📊 Detection Statistics</h3>
        <p>Unique Vehicles: <span id="uniqueVehicles">0</span></p>
        <p>Current Detections: <span id="currentDetections">0</span></p>
        <p>Frame Count: <span id="frameCount">0</span></p>
    </div>

    <script>
        const API_URL = 'http://localhost:5000/api/v1';
        const API_KEY = 'cctv_admin_[your-key]';
        const STREAM_ID = 'your-stream-id';
        
        let statsInterval;
        
        function startStream() {
            const streamUrl = `${API_URL}/streams/${STREAM_ID}/video_feed?api_key=${API_KEY}`;
            document.getElementById('liveStream').src = streamUrl;
            
            // Start updating stats
            if (statsInterval) clearInterval(statsInterval);
            statsInterval = setInterval(updateStats, 3000);
            
            console.log('🎥 Stream started');
        }
        
        function stopStream() {
            document.getElementById('liveStream').src = '';
            if (statsInterval) {
                clearInterval(statsInterval);
                statsInterval = null;
            }
            console.log('⏹️ Stream stopped');
        }
        
        function refreshStream() {
            const img = document.getElementById('liveStream');
            const currentSrc = img.src;
            img.src = '';
            setTimeout(() => {
                img.src = currentSrc + (currentSrc.includes('?') ? '&' : '?') + 't=' + Date.now();
            }, 100);
        }
        
        function takeScreenshot() {
            const video = document.getElementById('liveStream');
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = video.naturalWidth || video.width;
            canvas.height = video.naturalHeight || video.height;
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            const link = document.createElement('a');
            link.download = `cctv_screenshot_${Date.now()}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        async function updateStats() {
            try {
                const response = await fetch(`${API_URL}/streams/${STREAM_ID}/statistics`, {
                    headers: { 'X-API-Key': API_KEY }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const stats = data.data;
                        document.getElementById('uniqueVehicles').textContent = 
                            stats.tracking_stats.total_unique || 0;
                        document.getElementById('currentDetections').textContent = 
                            stats.tracking_stats.currently_tracked || 0;
                        document.getElementById('frameCount').textContent = 
                            stats.frame_count || 0;
                    }
                }
            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }
        
        // Auto-start stream when page loads
        window.onload = () => {
            console.log('🚀 CCTV Viewer Ready');
            // Uncomment to auto-start
            // startStream();
        };
    </script>
</body>
</html>
```

### **3. PHP - Server Integration**

```php
<?php
class CCTVStreamIntegration {
    private $apiUrl;
    private $apiKey;
    
    public function __construct($apiUrl, $apiKey) {
        $this->apiUrl = rtrim($apiUrl, '/');
        $this->apiKey = $apiKey;
    }
    
    public function getCurrentFrame($streamId) {
        $url = "{$this->apiUrl}/streams/{$streamId}/frame";
        $headers = [
            'X-API-Key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data['success']) {
                return $data['data'];
            }
        }
        
        return null;
    }
    
    public function saveCurrentFrame($streamId, $filename) {
        $frameData = $this->getCurrentFrame($streamId);
        
        if ($frameData && isset($frameData['frame_base64'])) {
            $imageData = base64_decode($frameData['frame_base64']);
            file_put_contents($filename, $imageData);
            
            echo "📸 Frame saved: {$filename}\n";
            echo "🚗 Detections: {$frameData['detection_count']}\n";
            echo "📊 Frame count: {$frameData['frame_count']}\n";
            
            return true;
        }
        
        return false;
    }
    
    public function getStreamUrl($streamId) {
        return "{$this->apiUrl}/streams/{$streamId}/video_feed?api_key={$this->apiKey}";
    }
    
    public function generateStreamHTML($streamId, $title = "CCTV Stream") {
        $streamUrl = $this->getStreamUrl($streamId);
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>{$title}</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; margin: 20px; }
                .stream { max-width: 100%; border: 2px solid #333; }
            </style>
        </head>
        <body>
            <h1>🎥 {$title}</h1>
            <img src='{$streamUrl}' class='stream' alt='Live CCTV Stream'>
            <p>Stream ID: {$streamId}</p>
        </body>
        </html>";
    }
}

// Usage Example
$cctv = new CCTVStreamIntegration(
    'http://localhost:5000/api/v1',
    'cctv_admin_[your-key]'
);

$streamId = 'your-stream-id';

// Save current frame
$cctv->saveCurrentFrame($streamId, "current_frame.jpg");

// Generate HTML viewer
$html = $cctv->generateStreamHTML($streamId, "Security Camera");
file_put_contents('stream_viewer.html', $html);
echo "📄 HTML viewer created: stream_viewer.html\n";

// Get stream URL for embedding
echo "🔗 Stream URL: " . $cctv->getStreamUrl($streamId) . "\n";
?>
```

### **4. Stream Sharing Example**

```bash
# 1. Share stream publicly
curl -X POST http://localhost:5000/api/v1/streams/{stream_id}/share \
  -H "X-API-Key: your-key" \
  -H "Content-Type: application/json" \
  -d '{
    "share_type": "public",
    "expires_at": "2024-08-02T00:00:00Z"
  }'

# 2. Access shared stream (no API key needed)
curl http://localhost:5000/api/v1/shared/streams/{stream_id}/video_feed

# 3. List all shared streams
curl http://localhost:5000/api/v1/shared/streams

# 4. Stop sharing
curl -X DELETE http://localhost:5000/api/v1/streams/{stream_id}/unshare \
  -H "X-API-Key: your-key"
```

## 🎯 Use Cases Praktis

### **1. Traffic Monitoring Dashboard**
```javascript
// Monitor multiple intersections
const intersections = [
    { id: 'intersection-1', name: 'Main Street' },
    { id: 'intersection-2', name: 'Park Avenue' },
    { id: 'intersection-3', name: 'Downtown' }
];

intersections.forEach(intersection => {
    const streamUrl = `${API_URL}/streams/${intersection.id}/video_feed?api_key=${API_KEY}`;
    
    const container = document.createElement('div');
    container.innerHTML = `
        <h3>${intersection.name}</h3>
        <img src="${streamUrl}" style="width: 300px;">
    `;
    document.body.appendChild(container);
});
```

### **2. Security Alert System**
```python
import time
import requests

def monitor_security(stream_id, api_key):
    """Monitor for security alerts"""
    api_url = "http://localhost:5000/api/v1"
    
    while True:
        try:
            # Get current detections
            response = requests.get(
                f"{api_url}/streams/{stream_id}/detections",
                headers={'X-API-Key': api_key}
            )
            
            if response.status_code == 200:
                data = response.json()
                detections = data['data']['detections']
                
                if len(detections) > 5:  # High traffic alert
                    send_alert(f"High traffic: {len(detections)} vehicles detected")
                
                # Log vehicle types
                for detection in detections:
                    print(f"🚗 {detection['vehicle_type']}: {detection['confidence']:.2f}")
            
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
        
        time.sleep(10)  # Check every 10 seconds

def send_alert(message):
    """Send alert notification"""
    print(f"🚨 ALERT: {message}")
    # Add your notification logic here (email, SMS, webhook, etc.)
```

### **3. Mobile App Integration**
```javascript
// React Native component
import React, { useState, useEffect } from 'react';
import { View, Image, Text, Button } from 'react-native';

const CCTVStreamComponent = ({ streamId, apiKey }) => {
    const [streamUrl, setStreamUrl] = useState('');
    const [isActive, setIsActive] = useState(false);
    const [stats, setStats] = useState({});
    
    const startStream = () => {
        const url = `http://localhost:5000/api/v1/streams/${streamId}/video_feed?api_key=${apiKey}`;
        setStreamUrl(url);
        setIsActive(true);
    };
    
    const stopStream = () => {
        setStreamUrl('');
        setIsActive(false);
    };
    
    useEffect(() => {
        if (isActive) {
            const interval = setInterval(async () => {
                try {
                    const response = await fetch(
                        `http://localhost:5000/api/v1/streams/${streamId}/statistics`,
                        { headers: { 'X-API-Key': apiKey } }
                    );
                    const data = await response.json();
                    if (data.success) {
                        setStats(data.data.tracking_stats);
                    }
                } catch (error) {
                    console.error('Stats error:', error);
                }
            }, 5000);
            
            return () => clearInterval(interval);
        }
    }, [isActive, streamId, apiKey]);
    
    return (
        <View style={{ padding: 20 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold' }}>CCTV Stream</Text>
            
            {streamUrl ? (
                <Image 
                    source={{ uri: streamUrl }} 
                    style={{ width: '100%', height: 200, marginVertical: 10 }}
                />
            ) : (
                <View style={{ height: 200, backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center' }}>
                    <Text>No Stream Active</Text>
                </View>
            )}
            
            <View style={{ flexDirection: 'row', justifyContent: 'space-around', marginVertical: 10 }}>
                <Button title="Start" onPress={startStream} disabled={isActive} />
                <Button title="Stop" onPress={stopStream} disabled={!isActive} />
            </View>
            
            <View style={{ marginTop: 10 }}>
                <Text>Unique Vehicles: {stats.total_unique || 0}</Text>
                <Text>Currently Tracked: {stats.currently_tracked || 0}</Text>
            </View>
        </View>
    );
};

export default CCTVStreamComponent;
```

## 🎉 Kesimpulan

Dengan implementasi video streaming API ini, project lain sekarang dapat:

✅ **Mengintegrasikan live video** dengan mudah  
✅ **Melihat deteksi real-time** langsung di aplikasi mereka  
✅ **Mengakses frame dan metadata** untuk analisis  
✅ **Berbagi stream** dengan kontrol akses yang fleksibel  
✅ **Membangun dashboard** monitoring yang komprehensif  

**Sistem CCTV Monitoring** sekarang menjadi platform yang dapat diintegrasikan dengan berbagai aplikasi dan sistem lain untuk monitoring traffic, security, dan analytics real-time!
