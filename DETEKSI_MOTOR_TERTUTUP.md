# Deteksi Motor Tertutup (Occluded Motorcycles)

## Konsep Dasar

Motor yang tertutup sebagian oleh kendaraan lain, pohon, atau objek lain sering tidak terdeteksi karena:

1. **Confidence rendah** - Hanya sebagian motor yang terlihat
2. **Bounding box tidak normal** - Aspect ratio aneh karena terpotong
3. **Ukuran sangat kecil** - Hanya bagian kecil yang terlihat
4. **Posisi relatif** - Berada di dekat/belakang kendaraan besar

## Algoritma Deteksi Motor Tertutup

### Langkah 1: Identif<PERSON><PERSON> Kandidat
```python
# Cari motor dengan confidence sangat rendah (0.1-0.25)
# Cari kendaraan besar di sekitar (mobil, bus, truk)
```

### Langkah 2: <PERSON><PERSON><PERSON>
**Skor Okklusi dihitung berdasarkan:**

1. **Proximity Score** (Jarak ke kendaraan besar)
   - Jarak < 150 pixel dari kendaraan besar = +1 poin
   - Sejajar secara vertikal (±50 pixel) = +1 poin

2. **Shape Anomaly Score** (Bentuk tidak normal)
   - Aspect ratio < 0.3 atau > 3.5 = +1 poin
   - Area sangat kecil (0.01%-0.1%) = +1 poin

3. **Size Anomaly Score** (Ukuran tidak wajar)
   - Width < 20 pixel = Kemungkinan tertutup
   - Height < 20 pixel = Kemungkinan tertutup

### Langkah 3: Validasi Okklusi
```python
if occlusion_score >= 2:
    # Anggap sebagai motor tertutup
    # Naikkan confidence (+0.08 per poin skor)
    # Perbaiki bounding box jika terlalu kecil
```

## Parameter Deteksi

### Confidence Threshold Khusus Motor
```python
# Motor: confidence > 0.15 (sangat rendah)
# Kendaraan lain: confidence > 0.3 (normal)
```

### Radius Deteksi Okklusi
```python
distance < 150  # 150 pixel dari kendaraan besar
vertical_align < 50  # Sejajar dalam 50 pixel
```

### Perbaikan Bounding Box
```python
if width < 30: expand_width_to_30px
if height < 30: expand_height_to_30px
```

## Visualisasi

### Kode Warna Motor
- 🔴 **Merah**: Motor normal (confidence > 0.4)
- 🟠 **Orange**: Motor berjajar (confidence 0.25-0.4) - "Motor*"
- 🟡 **Kuning**: Motor tertutup (confidence 0.15-0.25) - "Motor#"

### Informasi Display
```
ROI: Kuning=zona | Motor*=berjajar | Motor#=tertutup
Motor berjajar: 2
Motor tertutup: 1
```

## Contoh Skenario

### Skenario 1: Motor di Belakang Mobil
```
Input:
- Motor: conf=0.18, area=0.05%, ratio=0.2 (sangat tipis)
- Mobil: conf=0.65, jarak=80px, sejajar vertikal

Analisis:
- Proximity: +2 poin (dekat + sejajar)
- Shape: +1 poin (ratio aneh)
- Total: 3 poin

Output:
- Confidence: 0.18 + (3×0.08) = 0.42
- Status: DETECTED sebagai "Motor#"
```

### Skenario 2: Motor Tertutup Pohon
```
Input:
- Motor: conf=0.22, area=0.03%, width=15px
- Tidak ada kendaraan besar nearby

Analisis:
- Proximity: 0 poin
- Shape: +1 poin (width < 20)
- Total: 1 poin

Output:
- Confidence: 0.22 + (1×0.08) = 0.30
- Status: DETECTED sebagai "Motor#"
```

### Skenario 3: False Positive
```
Input:
- Objek: conf=0.20, area=0.02%
- Tidak ada kendaraan nearby
- Shape normal

Analisis:
- Total: 0 poin

Output:
- Status: REJECTED (tidak cukup indikator okklusi)
```

## Monitoring dan Debug

### Console Debug (tekan 'd')
```
=== DEBUG FRAME 123 ===
DETECTED: Motor# | Conf: 0.35 | Area: 0.4% | Ratio: 0.3 | Pos: (640,400) | Size_OK: True | ROI_OK: True
MISSED: Motor | Conf: 0.12 | Area: 0.2% | Ratio: 4.2 | Pos: (200,350) | Size_OK: False | ROI_OK: True
```

### Kontrol Real-time
- `d`: Debug detail semua deteksi
- `-`: Turunkan confidence (untuk menangkap lebih banyak motor tertutup)
- `+`: Naikkan confidence (jika terlalu banyak false positive)

## Tips Optimasi

### 1. Jika Motor Tertutup Masih Terlewat
```python
# Turunkan threshold confidence motor
if class_id == 3:
    confidence_ok = conf > 0.1  # Dari 0.15 ke 0.1
```

### 2. Jika Terlalu Banyak False Positive
```python
# Naikkan minimum occlusion score
if occlusion_score >= 3:  # Dari 2 ke 3
```

### 3. Sesuaikan Radius Deteksi
```python
if distance < 120:  # Dari 150 ke 120 (lebih ketat)
```

## Hasil yang Diharapkan

✅ **Motor di belakang mobil terdeteksi**
✅ **Motor tertutup pohon/tiang terdeteksi**
✅ **Motor sebagian keluar frame terdeteksi**
✅ **Visualisasi jelas dengan warna kuning**
✅ **Counter khusus motor tertutup**
✅ **Minimal false positive**

## Limitasi

⚠️ **Motor tertutup 90%+ mungkin tidak terdeteksi**
⚠️ **Objek sangat kecil bisa false positive**
⚠️ **Perlu fine-tuning per lokasi CCTV**

## Integrasi dengan Fitur Lain

Sistem bekerja bersamaan dengan:
- ✅ Deteksi motor berjajar
- ✅ Validasi ukuran objek
- ✅ Region of Interest (ROI)
- ✅ Debug mode lengkap

Total ada 3 jenis deteksi motor:
1. **Motor** - Normal (merah)
2. **Motor*** - Berjajar (orange)  
3. **Motor#** - Tertutup (kuning)
