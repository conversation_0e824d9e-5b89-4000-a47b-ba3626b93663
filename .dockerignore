# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/
examples/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Uploads and user data
uploads/
static/uploads/

# Model files (will be downloaded in container)
# Uncomment if you want to include pre-downloaded models
# *.pt
# *.weights

# Scripts
start_server.bat
start_server.sh

# Test files
test/
tests/
*_test.py
test_*.py
