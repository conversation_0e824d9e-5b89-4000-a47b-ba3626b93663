# CCTV Monitoring System - Serverless Deployment Guide

## Overview

This guide explains how to deploy the CCTV Monitoring System in serverless environments using Docker containers. The system supports multiple deployment options:

1. **Container-based serverless** (AWS Fargate, Google Cloud Run, Azure Container Instances)
2. **Function-based serverless** (AWS Lambda, Google Cloud Functions, Azure Functions)
3. **Traditional containerized deployment** (Docker, Kubernetes)

## Prerequisites

- Docker installed
- Cloud provider CLI tools (AWS CLI, gcloud, Azure CLI)
- Serverless Framework (for function-based deployment)

## Quick Start

### 1. Build Docker Image

```bash
# For regular deployment
docker build -t cctv-monitoring .

# For serverless deployment
docker build --target serverless -t cctv-monitoring-serverless .
```

### 2. Test Locally

```bash
# Regular deployment
docker run -p 5000:5000 cctv-monitoring

# Serverless deployment
docker run -p 8080:8080 -e PORT=8080 -e SERVERLESS=true cctv-monitoring-serverless
```

## Deployment Options

### Option 1: AWS Fargate (Recommended for Serverless)

#### Step 1: Build and Push to ECR

```bash
# Create ECR repository
aws ecr create-repository --repository-name cctv-monitoring

# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

# Build and tag image
docker build --target serverless -t cctv-monitoring-serverless .
docker tag cctv-monitoring-serverless:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/cctv-monitoring:latest

# Push image
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/cctv-monitoring:latest
```

#### Step 2: Deploy with AWS Fargate

```bash
# Create task definition (see aws-fargate-task.json)
aws ecs register-task-definition --cli-input-json file://aws-fargate-task.json

# Create service
aws ecs create-service \
  --cluster default \
  --service-name cctv-monitoring \
  --task-definition cctv-monitoring:1 \
  --desired-count 1 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
```

### Option 2: Google Cloud Run (✅ FIXED - Port 8080)

#### Step 1: Enable APIs and Set Variables
```bash
# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Set project variables
export PROJECT_ID="your-project-id"
export REGION="asia-southeast1"
export SERVICE_NAME="cctv-monitoring"
```

#### Step 2: Build and Push Image
```bash
# Build serverless target (IMPORTANT: Use serverless target)
docker build --target serverless -t cctv-monitoring-serverless .

# Tag for Google Container Registry
docker tag cctv-monitoring-serverless gcr.io/$PROJECT_ID/cctv-monitoring

# Configure Docker for GCR
gcloud auth configure-docker

# Push the image
docker push gcr.io/$PROJECT_ID/cctv-monitoring
```

#### Step 3: Deploy to Cloud Run
```bash
gcloud run deploy $SERVICE_NAME \
  --image gcr.io/$PROJECT_ID/cctv-monitoring \
  --platform managed \
  --region $REGION \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --cpu 2 \
  --timeout 300 \
  --concurrency 80 \
  --min-instances 0 \
  --max-instances 10 \
  --set-env-vars SERVERLESS=true,FLASK_ENV=production,FLASK_DEBUG=0
```

#### Step 4: Verify Deployment
```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region $REGION --format 'value(status.url)')

# Test health check
curl -f $SERVICE_URL/api/v1/system/health

# Expected response: {"status":"healthy","timestamp":"...","version":"1.0.0"}
```

### Option 3: Azure Container Instances

```bash
# Create resource group
az group create --name cctv-monitoring-rg --location eastus

# Build and push to Azure Container Registry
az acr build --registry myregistry --image cctv-monitoring .

# Deploy to Container Instances
az container create \
  --resource-group cctv-monitoring-rg \
  --name cctv-monitoring \
  --image myregistry.azurecr.io/cctv-monitoring:latest \
  --cpu 1 \
  --memory 2 \
  --ports 8080 \
  --environment-variables SERVERLESS=true PORT=8080
```

### Option 4: AWS Lambda (Function-based)

#### Prerequisites
```bash
npm install -g serverless
npm install serverless-python-requirements serverless-offline
pip install serverless-wsgi
```

#### Deploy
```bash
# Deploy to AWS Lambda
serverless deploy --stage prod

# For development
serverless offline start
```

## Environment Variables

### Required Variables
- `SERVERLESS=true` - Enables serverless mode
- `PORT=8080` - Port for the application (default: 5000)

### Optional Variables
- `FLASK_ENV=production` - Flask environment
- `FLASK_DEBUG=0` - Disable debug mode
- `MODEL_PATH=/app/models` - Path to YOLO models
- `LOG_LEVEL=INFO` - Logging level

## Performance Optimization

### Cold Start Optimization
1. **Pre-download models** - Models are downloaded during image build
2. **Use smaller models** - YOLOv5s for faster startup
3. **Optimize memory** - Allocate sufficient memory (1-2GB)

### Scaling Configuration
- **AWS Fargate**: Use Application Load Balancer with target tracking
- **Google Cloud Run**: Configure concurrency and max instances
- **Azure Container Instances**: Use Azure Load Balancer

## Monitoring and Logging

### Health Checks
- Endpoint: `/api/v1/system/health`
- Lightweight health check: `/health` (for Lambda)

### Logging
- Structured JSON logging
- CloudWatch (AWS), Cloud Logging (GCP), Azure Monitor

### Metrics
- Request count and latency
- Memory and CPU usage
- Model inference time

## Security Considerations

1. **API Keys**: Use environment variables or secret managers
2. **Network**: Configure security groups/firewall rules
3. **HTTPS**: Enable SSL/TLS termination at load balancer
4. **Authentication**: Implement proper API authentication

## Cost Optimization

### AWS Fargate
- Use Spot instances for development
- Configure auto-scaling based on CPU/memory
- Use reserved capacity for production

### Google Cloud Run
- Set appropriate concurrency limits
- Use minimum instances for warm starts
- Configure CPU allocation

### Azure Container Instances
- Use appropriate resource allocation
- Consider Azure Container Apps for auto-scaling

## Troubleshooting

### Common Issues

1. **Model Download Failures**
   - Ensure internet connectivity
   - Check disk space
   - Verify model URLs

2. **Memory Issues**
   - Increase memory allocation
   - Use smaller models
   - Optimize batch processing

3. **Cold Start Timeouts**
   - Increase timeout settings
   - Use provisioned concurrency
   - Implement health check warmup

### Debug Commands

```bash
# Check container logs
docker logs <container-id>

# Test health endpoint
curl http://localhost:8080/api/v1/system/health

# Check model loading
docker exec -it <container-id> python -c "from ultralytics import YOLO; YOLO('yolov5s.pt')"
```

## Next Steps

1. Set up monitoring and alerting
2. Configure CI/CD pipeline
3. Implement backup and disaster recovery
4. Scale based on usage patterns
5. Optimize costs based on usage

For more detailed configuration examples, see the `examples/` directory.
