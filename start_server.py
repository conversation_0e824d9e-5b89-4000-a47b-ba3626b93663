#!/usr/bin/env python3
"""
CCTV Monitoring System Startup Script
=====================================

This script starts the CCTV monitoring web application with proper configuration.
"""

import os
import sys
import subprocess
import platform
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'flask',
        'cv2',
        'torch',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            else:
                __import__(package)
            print(f"✓ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} is missing")
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    return True

def check_models():
    """Check if YOLOv5 models are available"""
    model_files = ['yolov5s.pt', 'yolov5m.pt']
    
    for model_file in model_files:
        if Path(model_file).exists():
            print(f"✓ {model_file} found")
            return True
    
    print("⚠ No local YOLOv5 models found. Models will be downloaded automatically on first run.")
    return True

def get_local_ip():
    """Get local IP address"""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "localhost"

def main():
    """Main startup function"""
    print("=" * 60)
    print("🎥 CCTV Monitoring System")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path('app.py').exists():
        print("❌ Error: app.py not found!")
        print("Please run this script from the CCTV project directory.")
        sys.exit(1)
    
    # Check dependencies
    print("\n📦 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    # Check models
    print("\n🤖 Checking AI models...")
    check_models()
    
    # Get network info
    local_ip = get_local_ip()
    port = 5000
    
    print(f"\n🌐 Starting web server...")
    print(f"   Local URL:    http://localhost:{port}")
    print(f"   Network URL:  http://{local_ip}:{port}")
    print(f"   Platform:     {platform.system()} {platform.release()}")
    
    # Set environment variables
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    # Start the Flask application
    try:
        print(f"\n🚀 Launching application...")
        print("   Press Ctrl+C to stop the server")
        print("=" * 60)
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{port}')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Import and run the Flask app
        from app import app
        app.run(debug=True, host='0.0.0.0', port=port, threaded=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
