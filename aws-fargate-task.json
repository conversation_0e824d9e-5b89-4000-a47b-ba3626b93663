{"family": "cctv-monitoring", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::ACCOUNT-ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT-ID:role/ecsTaskRole", "containerDefinitions": [{"name": "cctv-monitoring", "image": "ACCOUNT-ID.dkr.ecr.us-east-1.amazonaws.com/cctv-monitoring:latest", "portMappings": [{"containerPort": 8080, "protocol": "tcp"}], "environment": [{"name": "SERVERLESS", "value": "true"}, {"name": "PORT", "value": "8080"}, {"name": "FLASK_ENV", "value": "production"}, {"name": "FLASK_DEBUG", "value": "0"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/cctv-monitoring", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8080/api/v1/system/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "essential": true}]}