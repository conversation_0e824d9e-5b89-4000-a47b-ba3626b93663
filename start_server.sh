#!/bin/bash

# CCTV Monitoring System Startup Script for Linux/macOS

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}============================================================${NC}"
echo -e "${BLUE}                🎥 CCTV Monitoring System${NC}"
echo -e "${BLUE}============================================================${NC}"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python 3 is not installed${NC}"
    echo "Please install Python 3.8+ from https://python.org"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ Error: app.py not found!${NC}"
    echo "Please run this script from the CCTV project directory."
    exit 1
fi

echo -e "${GREEN}📦 Checking Python installation...${NC}"
python3 --version

echo
echo -e "${GREEN}🚀 Starting CCTV Monitoring System...${NC}"
echo "   Press Ctrl+C to stop the server"
echo "   The web interface will open automatically"
echo -e "${BLUE}============================================================${NC}"
echo

# Make the script executable
chmod +x start_server.py

# Start the Python application
python3 start_server.py

echo
echo -e "${YELLOW}👋 Server stopped${NC}"
