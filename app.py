from flask import Flask, render_template, request, Response, jsonify, session
import cv2
import torch
import numpy as np
import time
import threading
import uuid
from collections import defaultdict
import json
import logging
from urllib.parse import urlparse

app = Flask(__name__)
app.secret_key = 'cctv_monitoring_secret_key_2024'

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Register API blueprint
from api import api_bp
app.register_blueprint(api_bp)

# Global variables for managing streams
active_streams = {}
stream_lock = threading.Lock()

class CCTVStream:
    """Class to manage individual CCTV stream processing"""
    
    def __init__(self, stream_url, stream_id):
        self.stream_url = stream_url
        self.stream_id = stream_id
        self.cap = None
        self.is_active = False
        self.model = None
        self.vehicle_tracker = None
        self.frame_count = 0
        self.last_frame = None
        self.detection_stats = {
            'total_detections': 0,
            'unique_vehicles': 0,
            'by_type': {}
        }
        
        # Initialize YOLO model
        self.init_model()
        
        # Initialize vehicle tracker (we'll import this from detection service)
        from detection_service import VehicleTracker
        self.vehicle_tracker = VehicleTracker()
    
    def init_model(self):
        """Initialize YOLOv5 model"""
        try:
            logger.info("Loading YOLOv5 model...")
            self.model = torch.hub.load('ultralytics/yolov5', 'yolov5m')
            self.model.conf = 0.15
            self.model.iou = 0.2
            self.model.max_det = 1000
            logger.info("YOLOv5 model loaded successfully!")
        except Exception as e:
            logger.error(f"Error loading YOLOv5 model: {e}")
            try:
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s')
                logger.info("YOLOv5s model loaded as fallback!")
            except Exception as e2:
                logger.error(f"Failed to load any model: {e2}")
                raise
    
    def connect_stream(self):
        """Connect to the video stream"""
        try:
            self.cap = cv2.VideoCapture(self.stream_url)
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            if self.cap.isOpened():
                self.is_active = True
                logger.info(f"Successfully connected to stream: {self.stream_url}")
                return True
            else:
                logger.error(f"Failed to open stream: {self.stream_url}")
                return False
        except Exception as e:
            logger.error(f"Error connecting to stream: {e}")
            return False
    
    def disconnect_stream(self):
        """Disconnect from the video stream"""
        self.is_active = False
        if self.cap:
            self.cap.release()
            self.cap = None
        logger.info(f"Disconnected from stream: {self.stream_url}")
    
    def process_frame(self):
        """Process a single frame with object detection"""
        if not self.cap or not self.is_active:
            return None

        try:
            ret, frame = self.cap.read()
            if not ret:
                logger.warning("Failed to read frame from stream")
                # Try to reconnect
                if self.frame_count > 0:  # Only try reconnect if we had frames before
                    logger.info("Attempting to reconnect to stream...")
                    self.disconnect_stream()
                    time.sleep(1)
                    if self.connect_stream():
                        logger.info("Successfully reconnected to stream")
                        return self.last_frame  # Return last known good frame
                return None

            # Validate frame
            if frame is None or frame.size == 0:
                logger.warning("Received empty frame")
                return self.last_frame

            # Resize frame for processing
            try:
                frame = cv2.resize(frame, (1280, 720))
            except Exception as e:
                logger.error(f"Error resizing frame: {e}")
                return self.last_frame

            self.frame_count += 1

            # Enhance frame for better detection
            enhanced_frame = cv2.convertScaleAbs(frame, alpha=1.15, beta=15)

            # Run YOLO detection with error handling
            try:
                results = self.model(enhanced_frame)
                detections = results.xyxy[0].cpu().numpy()
            except Exception as e:
                logger.error(f"Error in YOLO detection: {e}")
                # Return frame without detection overlay
                self.add_overlay_info(frame, {'total_unique': 0, 'by_type': {}})
                return frame

            # Process detections
            try:
                from detection_service import process_detections, VEHICLE_CLASSES

                valid_detections, processed_frame = process_detections(
                    detections, frame, VEHICLE_CLASSES
                )

                # Update vehicle tracking
                self.vehicle_tracker.update(valid_detections, self.frame_count)

                # Update statistics
                tracking_stats = self.vehicle_tracker.get_statistics()
                self.detection_stats.update({
                    'total_detections': self.detection_stats['total_detections'] + len(valid_detections),
                    'unique_vehicles': tracking_stats['total_unique'],
                    'by_type': tracking_stats['by_type']
                })

                # Add overlay information
                self.add_overlay_info(processed_frame, tracking_stats)

                self.last_frame = processed_frame
                return processed_frame

            except Exception as e:
                logger.error(f"Error processing detections: {e}")
                # Return frame with basic overlay
                self.add_overlay_info(frame, {'total_unique': 0, 'by_type': {}})
                return frame

        except Exception as e:
            logger.error(f"Critical error in frame processing: {e}")
            return self.last_frame
    
    def add_overlay_info(self, frame, tracking_stats):
        """Add information overlay to the frame"""
        # Add statistics text
        info_text = f"Unique Vehicles: {tracking_stats['total_unique']} | Frame: {self.frame_count}"
        cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 1)
        
        # Add vehicle type breakdown
        y_offset = 60
        if tracking_stats['by_type']:
            for vehicle_type, count in tracking_stats['by_type'].items():
                type_text = f"{vehicle_type}: {count}"
                cv2.putText(frame, type_text, (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                y_offset += 25
        
        # Add stream info
        stream_info = f"Stream: {self.stream_url[:50]}..."
        cv2.putText(frame, stream_info, (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

def validate_stream_url(url):
    """Validate if the provided URL is a valid stream URL"""
    try:
        # Basic URL format validation
        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            return False, "Invalid URL format. Please include http:// or https://"

        # Check supported schemes
        if parsed.scheme.lower() not in ['http', 'https', 'rtsp', 'rtmp']:
            return False, "Unsupported URL scheme. Supported: HTTP, HTTPS, RTSP, RTMP"

        # Check for common stream file extensions
        valid_extensions = ['.m3u8', '.mp4', '.avi', '.mov', '.flv', '.ts']
        path_lower = parsed.path.lower()

        # If it's not a direct file, it might be a streaming endpoint
        is_stream_file = any(path_lower.endswith(ext) for ext in valid_extensions)
        is_stream_endpoint = 'stream' in path_lower or 'live' in path_lower

        if not is_stream_file and not is_stream_endpoint and parsed.path and parsed.path != '/':
            logger.warning(f"URL might not be a valid stream: {url}")

        # Try to connect to the stream with timeout
        cap = cv2.VideoCapture(url)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        # Set a timeout for connection attempt
        start_time = time.time()
        timeout = 10  # 10 seconds timeout

        while time.time() - start_time < timeout:
            if cap.isOpened():
                # Try to read a frame to verify it's actually working
                ret, frame = cap.read()
                cap.release()

                if ret and frame is not None:
                    return True, "Valid stream URL"
                else:
                    return False, "Stream URL accessible but no video data received"
            time.sleep(0.1)

        cap.release()
        return False, "Connection timeout. Stream may be unavailable or slow to respond"

    except Exception as e:
        logger.error(f"Error validating URL {url}: {e}")
        return False, f"Error validating URL: {str(e)}"

@app.route('/')
def index():
    """Main page with stream input form"""
    return render_template('index.html')

@app.route('/start_stream', methods=['POST'])
def start_stream():
    """Start processing a new stream"""
    try:
        data = request.get_json()
        stream_url = data.get('stream_url', '').strip()
        
        if not stream_url:
            return jsonify({'success': False, 'error': 'Stream URL is required'})
        
        # Validate stream URL
        is_valid, message = validate_stream_url(stream_url)
        if not is_valid:
            return jsonify({'success': False, 'error': message})
        
        # Generate unique stream ID
        stream_id = str(uuid.uuid4())
        
        # Create new stream instance
        cctv_stream = CCTVStream(stream_url, stream_id)
        
        # Connect to stream
        if cctv_stream.connect_stream():
            with stream_lock:
                active_streams[stream_id] = cctv_stream

            # Store stream ID in session
            session['current_stream_id'] = stream_id

            # Send webhook notification for stream started
            try:
                from api import send_webhook_notification
                send_webhook_notification('stream_started', {
                    'stream_id': stream_id,
                    'stream_url': stream_url
                })
            except Exception as e:
                logger.warning(f"Failed to send stream started webhook: {e}")

            return jsonify({
                'success': True,
                'stream_id': stream_id,
                'message': 'Stream started successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to connect to stream'})
            
    except Exception as e:
        logger.error(f"Error starting stream: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/stop_stream', methods=['POST'])
def stop_stream():
    """Stop the current stream"""
    try:
        stream_id = session.get('current_stream_id')
        if not stream_id:
            return jsonify({'success': False, 'error': 'No active stream'})
        
        with stream_lock:
            if stream_id in active_streams:
                stream_url = active_streams[stream_id].stream_url
                active_streams[stream_id].disconnect_stream()
                del active_streams[stream_id]

                # Send webhook notification for stream stopped
                try:
                    from api import send_webhook_notification
                    send_webhook_notification('stream_stopped', {
                        'stream_id': stream_id,
                        'stream_url': stream_url
                    })
                except Exception as e:
                    logger.warning(f"Failed to send stream stopped webhook: {e}")

        session.pop('current_stream_id', None)

        return jsonify({'success': True, 'message': 'Stream stopped successfully'})
        
    except Exception as e:
        logger.error(f"Error stopping stream: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/stream_stats')
def stream_stats():
    """Get current stream statistics"""
    try:
        stream_id = session.get('current_stream_id')
        if not stream_id or stream_id not in active_streams:
            return jsonify({'success': False, 'error': 'No active stream'})
        
        stream = active_streams[stream_id]
        stats = stream.detection_stats.copy()
        stats['frame_count'] = stream.frame_count
        stats['is_active'] = stream.is_active
        
        return jsonify({'success': True, 'stats': stats})
        
    except Exception as e:
        logger.error(f"Error getting stream stats: {e}")
        return jsonify({'success': False, 'error': str(e)})

def generate_frames(stream_id):
    """Generate video frames for streaming"""
    frame_count = 0
    while True:
        with stream_lock:
            if stream_id not in active_streams:
                logger.info(f"Stream {stream_id} not found in active streams")
                break

            stream = active_streams[stream_id]

        if not stream.is_active:
            logger.info(f"Stream {stream_id} is not active")
            break

        try:
            frame = stream.process_frame()
            if frame is not None:
                frame_count += 1

                # Encode frame as JPEG
                ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                else:
                    logger.warning("Failed to encode frame")
            else:
                # If frame is None, try to reconnect
                logger.warning("Received None frame, attempting reconnection")
                if not stream.connect_stream():
                    logger.error("Failed to reconnect stream")
                    break

        except Exception as e:
            logger.error(f"Error in frame generation: {e}")
            break

        time.sleep(0.033)  # ~30 FPS

    logger.info(f"Frame generation stopped for stream {stream_id} after {frame_count} frames")

@app.route('/video_feed')
def video_feed():
    """Video streaming route"""
    stream_id = session.get('current_stream_id')
    if not stream_id or stream_id not in active_streams:
        return "No active stream", 404
    
    return Response(generate_frames(stream_id),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)
