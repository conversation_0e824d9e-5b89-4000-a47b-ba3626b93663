# Quick Start - CCTV Monitoring Serverless Deployment

## 1. Build and Test Locally

```bash
# Build for serverless
docker build --target serverless -t cctv-monitoring-serverless .

# Test locally
docker run -p 8080:8080 -e PORT=8080 -e SERVERLESS=true cctv-monitoring-serverless

# Test API
curl http://localhost:8080/api/v1/system/health
```

## 2. Deploy to Cloud

### AWS Fargate (Recommended)
```bash
# Build and push to ECR
aws ecr create-repository --repository-name cctv-monitoring
docker tag cctv-monitoring-serverless:latest YOUR-ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/cctv-monitoring:latest
docker push YOUR-ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/cctv-monitoring:latest

# Deploy
aws ecs register-task-definition --cli-input-json file://aws-fargate-task.json
```

### Google Cloud Run
```bash
gcloud builds submit --tag gcr.io/PROJECT-ID/cctv-monitoring
gcloud run deploy cctv-monitoring --image gcr.io/PROJECT-ID/cctv-monitoring --platform managed --allow-unauthenticated
```

### Azure Container Instances
```bash
az acr build --registry myregistry --image cctv-monitoring .
az container create --resource-group rg --name cctv-monitoring --image myregistry.azurecr.io/cctv-monitoring:latest
```

## 3. Environment Variables

- `SERVERLESS=true` - Enable serverless mode
- `PORT=8080` - Application port
- `FLASK_ENV=production` - Production environment

Your CCTV monitoring system is now ready for serverless deployment! 🎉
