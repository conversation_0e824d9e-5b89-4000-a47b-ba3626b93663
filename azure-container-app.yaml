apiVersion: apps/v1
kind: Deployment
metadata:
  name: cctv-monitoring
  labels:
    app: cctv-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cctv-monitoring
  template:
    metadata:
      labels:
        app: cctv-monitoring
    spec:
      containers:
      - name: cctv-monitoring
        image: myregistry.azurecr.io/cctv-monitoring:latest
        ports:
        - containerPort: 8080
        env:
        - name: SERVERLESS
          value: "true"
        - name: PORT
          value: "8080"
        - name: FLASK_ENV
          value: "production"
        - name: FLASK_DEBUG
          value: "0"
        resources:
          requests:
            memory: "1Gi"
            cpu: "0.5"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /api/v1/system/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/system/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: cctv-monitoring-service
spec:
  selector:
    app: cctv-monitoring
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
