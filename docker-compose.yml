version: '3.8'

services:
  cctv-monitoring:
    build:
      context: .
      dockerfile: Dockerfile
      target: application  # Use 'serverless' for serverless deployment
    ports:
      - "5001:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=0
      - SERVERLESS=false
    volumes:
      # Mount for persistent model storage (optional)
      - ./models:/app/models
      # Mount for logs (optional)
      - ./logs:/app/logs
      # Mount for uploads (optional)
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - cctv-network

  # Optional: Redis for caching (for production)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - cctv-network
    profiles:
      - production

  # Optional: Nginx reverse proxy (for production)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - cctv-monitoring
    restart: unless-stopped
    networks:
      - cctv-network
    profiles:
      - production

networks:
  cctv-network:
    driver: bridge

volumes:
  redis_data:

# Serverless deployment example (uncomment and modify as needed)
# services:
#   cctv-serverless:
#     build:
#       context: .
#       dockerfile: Dockerfile
#       target: serverless
#     ports:
#       - "8080:8080"
#     environment:
#       - PORT=8080
#       - SERVERLESS=true
#       - FLASK_ENV=production
#       - FLASK_DEBUG=0
#     restart: unless-stopped
