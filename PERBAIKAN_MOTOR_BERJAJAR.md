# Perbaikan Deteksi Motor Berjajar

## Masalah yang Ditemukan

Motor yang berjajar/berkelompok sering tidak terdeteksi dengan benar karena:

1. **Non-Maximum Suppression (NMS) terlalu agresif**
   - IoU threshold 0.45 terlalu tinggi
   - Menghapus deteksi motor yang berdekatan

2. **Confidence threshold terlalu ketat untuk motor kecil**
   - Motor yang jauh atau sebagian tertutup memiliki confidence rendah
   - Sistem menolak deteksi dengan confidence < 0.3

3. **Validasi ukuran terlalu ketat**
   - Motor berjajar bisa terlihat lebih kecil atau terpotong
   - Aspect ratio berubah karena perspektif

## Perbaikan yang Dilakukan

### 1. Penyesuaian NMS (Non-Maximum Suppression)
```python
# Sebelum
model.iou = 0.45

# Sesudah
model.iou = 0.3  # Lebih rendah untuk mendeteksi objek berdekatan
```

### 2. Algoritma Deteksi Motor Berjajar
Ditambahkan fungsi `detect_clustered_motorcycles()` yang:
- **Mengidentifikasi motor dengan confidence rendah-sedang** (0.15-0.4)
- **Mencari motor lain dalam radius 100 pixel**
- **Menaikkan confidence** motor yang berjajar (+0.15)
- **Menambahkan deteksi tambahan** ke hasil akhir

### 3. Validasi Khusus untuk Motor
**Parameter motor diperlonggar:**
- **Area**: 0.01%-20% (dari 0.02%-15%)
- **Aspect ratio**: 0.3-4.0 (dari 0.5-3.0)

**Logika deteksi khusus motor:**
- Motor dengan confidence 0.3-0.4 hanya perlu validasi ukuran ATAU ROI
- Kendaraan lain tetap perlu kedua validasi

### 4. Visualisasi Motor Berjajar
- **Motor confidence tinggi**: Kotak merah
- **Motor berjajar**: Kotak orange dengan label "Motor*"
- **Counter khusus**: Menampilkan jumlah motor berjajar

## Cara Kerja Algoritma

### Langkah 1: Deteksi Awal
```
YOLOv5 → Deteksi semua objek → Filter confidence > 0.3
```

### Langkah 2: Analisis Motor Berjajar
```
Motor conf 0.15-0.4 → Cari motor lain dalam radius 100px → Naikkan confidence
```

### Langkah 3: Gabungkan Hasil
```
Deteksi asli + Deteksi tambahan motor berjajar → Validasi final
```

### Langkah 4: Visualisasi
```
Motor biasa: Merah | Motor berjajar: Orange + tanda *
```

## Contoh Skenario

### Skenario 1: 3 Motor Berjajar
```
Motor A: conf 0.25 → Terdeteksi motor B & C nearby → conf naik ke 0.40 → DETECTED
Motor B: conf 0.30 → Terdeteksi motor A & C nearby → conf naik ke 0.45 → DETECTED  
Motor C: conf 0.22 → Terdeteksi motor A & B nearby → conf naik ke 0.37 → DETECTED
```

### Skenario 2: Motor Tunggal
```
Motor A: conf 0.25 → Tidak ada motor nearby → conf tetap 0.25 → REJECTED
```

## Parameter yang Dapat Disesuaikan

### 1. Radius Deteksi Berjajar
```python
if distance < 100:  # Ubah nilai ini untuk menyesuaikan sensitivitas
```

### 2. Range Confidence Motor Berjajar
```python
if class_id == 3 and 0.15 <= conf <= 0.4:  # Sesuaikan range ini
```

### 3. Boost Confidence
```python
adjusted_conf = min(0.5, conf1 + 0.15)  # Sesuaikan nilai boost
```

## Monitoring dan Debug

### Kontrol Keyboard
- `d`: Debug detail - lihat semua deteksi motor
- `+`/`-`: Adjust confidence threshold
- `r`: Reset counter

### Informasi Visual
- **Garis kuning**: ROI zona deteksi
- **Kotak orange**: Motor berjajar (Motor*)
- **Counter khusus**: "Motor berjajar terdeteksi: X"

### Console Debug (tekan 'd')
```
=== DEBUG FRAME 123 ===
DETECTED: Motor* | Conf: 0.37 | Area: 0.8% | Ratio: 1.2 | Pos: (640,400) | Size_OK: True | ROI_OK: True
DETECTED: Motor* | Conf: 0.42 | Area: 0.9% | Ratio: 1.1 | Pos: (680,405) | Size_OK: True | ROI_OK: True
MISSED: Motor | Conf: 0.18 | Area: 0.5% | Ratio: 0.9 | Pos: (200,350) | Size_OK: False | ROI_OK: True
```

## Hasil yang Diharapkan

✅ **Motor berjajar terdeteksi lebih baik**
✅ **Visualisasi jelas dengan warna berbeda**  
✅ **Counter khusus untuk motor berjajar**
✅ **Debug info lengkap untuk analisis**

## Tips Penggunaan

1. **Jika masih banyak motor terlewat**: Turunkan confidence dengan tombol `-`
2. **Jika terlalu banyak false positive**: Naikkan confidence dengan tombol `+`
3. **Untuk analisis detail**: Tekan `d` untuk melihat semua deteksi di console
4. **Perhatikan warna kotak**: Orange = motor berjajar, Merah = motor biasa
