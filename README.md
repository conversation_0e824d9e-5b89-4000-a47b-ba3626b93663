# CCTV Monitoring System

Sistem pemantauan CCTV berbasis web dengan deteksi objek real-time menggunakan YOLOv5. Sistem ini memungkinkan pengguna untuk memasukkan URL stream kustom dan melihat hasil deteksi kendaraan secara langsung di browser. **Dilengkapi dengan REST API untuk integrasi dengan project lain dan live video streaming.**

## 🚀 Fitur Utama

### 🎥 **Web Interface**
- **Custom Stream URL**: Input URL stream CCTV dari berbagai sumber (HTTP, HTTPS, RTSP, M3U8)
- **Vehicle Detection**: Deteksi real-time untuk mobil, motor, bus, truk, dan sepeda
- **Live Statistics**: Statistik deteksi real-time dengan breakdown per jenis kendaraan
- **Vehicle Tracking**: Sistem tracking untuk menghitung kendaraan unik
- **Responsive Design**: Interface web yang responsif dan user-friendly
- **Configurable Settings**: Pengaturan confidence threshold dan filtering
- **Mobile Responsive**: Dapat diakses dari perangkat mobile

### 📡 **REST API untuk Integrasi**
- **Stream Management**: Create, read, update, delete streams via API
- **Real-time Detection Data**: Akses data deteksi dan statistik secara programmatik
- **Live Video Streaming**: Stream video dengan overlay deteksi melalui API
- **Webhook Notifications**: Real-time notifications untuk events
- **API Key Authentication**: Secure access dengan permission levels
- **Stream Sharing**: Public/private stream sharing dengan access control

### 🎬 **Live Video Streaming API**
- **MJPEG Streaming**: Live video stream dengan detection overlay
- **Frame Capture**: Akses frame saat ini dalam format base64
- **Multi-platform Support**: Python, JavaScript, PHP, HTML clients
- **Shared Streams**: Berbagi stream untuk akses publik atau terbatas
- **Real-time Processing**: Video processing dengan YOLOv5 real-time

## Teknologi

- **Backend**: Flask (Python)
- **AI/ML**: YOLOv5 (PyTorch)
- **Computer Vision**: OpenCV
- **Frontend**: Bootstrap 5, jQuery
- **Real-time Streaming**: MJPEG over HTTP

## Instalasi

### 1. Clone Repository

```bash
git clone <repository-url>
cd cctv
```

### 2. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt

# Untuk Ubuntu/Debian (opsional)
sudo apt-get update
sudo apt-get install python3-opencv libglib2.0-0 libsm6 libxext6 libxrender-dev libgl1-mesa-glx

# Untuk macOS (opsional)
brew install opencv
```

### 3. Download YOLOv5 Models (Opsional)

Model akan didownload otomatis saat pertama kali dijalankan, tapi Anda bisa download manual:

```bash
# Download YOLOv5 models
wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.pt
wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5m.pt
```

## 🚀 Quick Start

### 1. Instalasi dan Setup

```bash
# Clone repository
git clone <repository-url>
cd cctv

# Install dependencies
pip install -r requirements.txt

# Jalankan aplikasi
python app.py
```

Aplikasi akan berjalan di `http://localhost:5000`

### 2. Web Interface

1. Buka browser dan kunjungi `http://localhost:5000`
2. Masukkan URL stream CCTV di field "Stream URL"
3. Klik "Start Stream" untuk memulai monitoring
4. Lihat hasil deteksi real-time di panel video

### 3. API Integration

```bash
# Health check
curl http://localhost:5000/api/v1/system/health

# Create stream via API
curl -X POST http://localhost:5000/api/v1/streams \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"stream_url": "http://example.com/stream.m3u8"}'

# Access live video stream
http://localhost:5000/api/v1/streams/{stream_id}/video_feed?api_key=your-key
```

### 4. Contoh URL Stream

```
# M3U8 Stream (HLS)
http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8

# RTSP Stream
rtsp://username:password@ip:port/stream

# HTTP Stream
http://example.com/live/stream.m3u8
```

## Konfigurasi

### Detection Settings

- **Confidence Threshold**: Mengatur tingkat kepercayaan minimum untuk deteksi (0.05 - 0.9)
- **Strict Filtering**: Filter ketat untuk mengurangi false positive
- **Motorcycle Mode**: Mode sensitif khusus untuk deteksi motor

### Supported Stream Formats

- HTTP Live Streaming (HLS) - `.m3u8`
- RTSP (Real Time Streaming Protocol)
- HTTP Progressive Download - `.mp4`, `.avi`, `.mov`
- RTMP (Real Time Messaging Protocol)

## 📡 API Endpoints

### Web Interface API

```
POST /start_stream         # Start stream in web interface
POST /stop_stream          # Stop stream in web interface
GET /stream_stats          # Get stream statistics
GET /video_feed            # Video feed for web interface
```

### REST API (v1)

#### Stream Management
```
GET /api/v1/streams                    # List all streams
POST /api/v1/streams                   # Create new stream
GET /api/v1/streams/{id}               # Get stream details
DELETE /api/v1/streams/{id}            # Delete stream
```

#### Detection & Statistics
```
GET /api/v1/streams/{id}/detections    # Get current detections
GET /api/v1/streams/{id}/statistics    # Get stream statistics
DELETE /api/v1/streams/{id}/statistics # Reset statistics
```

#### Video Streaming
```
GET /api/v1/streams/{id}/video_feed    # Live video stream with detection overlay
GET /api/v1/streams/{id}/frame         # Current frame as base64 with metadata
GET /api/v1/streams/{id}/stream_info   # Stream capabilities and endpoints
```

#### Stream Sharing
```
POST /api/v1/streams/{id}/share        # Share stream (public/private)
DELETE /api/v1/streams/{id}/unshare    # Stop sharing stream
GET /api/v1/shared/streams             # List shared streams
GET /api/v1/shared/streams/{id}/video_feed  # Access shared stream
```

#### System & Authentication
```
GET /api/v1/system/health              # System health check
GET /api/v1/system/status              # System status and resources
GET /api/v1/auth/keys                  # List API keys (admin)
POST /api/v1/auth/keys                 # Create API key (admin)
```

#### Webhooks
```
GET /api/v1/webhooks                   # List webhooks
POST /api/v1/webhooks                  # Register webhook
DELETE /api/v1/webhooks/{id}           # Delete webhook
```

## 📁 Struktur Project

```
cctv/
├── 🌐 Core Application
│   ├── app.py                      # Main Flask application
│   ├── api.py                      # REST API endpoints
│   ├── api_config.py               # API configuration
│   ├── detection_service.py        # Detection and tracking logic
│   └── requirements.txt            # Python dependencies
│
├── 🎨 Web Interface
│   ├── templates/                  # HTML templates
│   │   ├── base.html              # Base template
│   │   └── index.html             # Main interface
│   └── static/                    # Static assets
│       ├── css/style.css          # Styling
│       └── js/main.js             # Frontend JavaScript
│
├── 💻 API Client Examples
│   ├── python/
│   │   ├── cctv_api_client.py     # Python API client
│   │   └── video_streaming_client.py  # Video streaming client
│   ├── javascript/
│   │   ├── cctv-api-client.js     # JavaScript API client
│   │   └── video-streaming-client.js  # Video streaming client
│   ├── php/
│   │   └── CCTVAPIClient.php      # PHP API client
│   ├── html/
│   │   └── stream_viewer.html     # HTML stream viewer
│   └── README.md                  # Client examples guide
│
├── 📚 Documentation
│   ├── README.md                  # Main documentation
│   ├── API_DOCUMENTATION.md       # Complete API docs
│   ├── API_INTEGRATION_GUIDE.md   # Integration guide
│   ├── VIDEO_STREAMING_API_GUIDE.md  # Video streaming guide
│   ├── STREAMING_INTEGRATION_EXAMPLES.md  # Integration examples
│   └── PROJECT_OVERVIEW.md        # Project overview
│
├── 🚀 Startup Scripts
│   ├── start_server.py            # Python startup script
│   ├── start_server.bat           # Windows batch file
│   └── start_server.sh            # Linux/macOS shell script
│
└── 🤖 AI Models
    └── *.pt                       # YOLOv5 model files (auto-downloaded)
```

## Troubleshooting

### Common Issues

1. **Stream tidak bisa connect**
   - Pastikan URL stream valid dan dapat diakses
   - Cek firewall dan network connectivity
   - Beberapa stream memerlukan authentication

2. **Deteksi tidak akurat**
   - Adjust confidence threshold
   - Enable/disable strict filtering
   - Gunakan motorcycle mode untuk deteksi motor yang lebih sensitif

3. **Performance lambat**
   - Gunakan model YOLOv5s untuk performa lebih cepat
   - Kurangi resolusi stream jika memungkinkan
   - Pastikan hardware memadai (GPU recommended)

### System Requirements

- **Minimum**: Python 3.8+, 4GB RAM, CPU dengan 4 cores
- **Recommended**: Python 3.9+, 8GB RAM, GPU dengan CUDA support
- **OS**: Windows 10+, Ubuntu 18.04+, macOS 10.15+

## 💻 Integration Examples

### Python Integration

```python
from examples.python.cctv_api_client import CCTVAPIClient

# Initialize client
client = CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key')

# Create and monitor stream
stream = client.create_stream('http://your-stream-url.com')
detections = client.get_detections(stream['stream_id'])
print(f"Found {len(detections)} vehicles")

# Live video streaming
from examples.python.video_streaming_client import CCTVVideoStreamClient
video_client = CCTVVideoStreamClient('http://localhost:5000/api/v1', 'your-api-key')
video_client.stream_video_opencv('your-stream-id')
```

### JavaScript Integration

```javascript
const { CCTVAPIClient } = require('./examples/javascript/cctv-api-client');

const client = new CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key');

// Create stream and get detections
const stream = await client.createStream('http://your-stream-url.com');
const detections = await client.getDetections(stream.stream_id);
console.log(`Found ${detections.length} vehicles`);

// Live video in HTML
const streamUrl = `http://localhost:5000/api/v1/streams/${stream.stream_id}/video_feed?api_key=your-key`;
document.getElementById('video').src = streamUrl;
```

### PHP Integration

```php
require_once 'examples/php/CCTVAPIClient.php';

$client = new CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key');

// Create stream and get detections
$stream = $client->createStream('http://your-stream-url.com');
$detections = $client->getDetections($stream['stream_id']);
echo "Found " . count($detections) . " vehicles\n";
```

### HTML Direct Embedding

```html
<!-- Live CCTV Stream -->
<img src="http://localhost:5000/api/v1/shared/streams/your-stream-id/video_feed"
     style="width: 100%;" alt="Live CCTV Stream">

<!-- Or use the complete viewer -->
<iframe src="examples/html/stream_viewer.html" width="100%" height="600"></iframe>
```

## 🎯 Use Cases

### 1. Traffic Management System
- Monitor multiple intersections simultaneously
- Real-time traffic density analysis
- Automatic traffic light control based on vehicle count
- Traffic flow optimization

### 2. Security Monitoring
- Multi-camera surveillance dashboard
- Real-time alert system for unusual activity
- Automated incident detection and reporting
- Integration with security systems

### 3. Smart City Integration
- Public transportation monitoring
- Parking space management
- Environmental monitoring (vehicle emissions)
- Urban planning data collection

### 4. Business Intelligence
- Customer traffic analysis for retail
- Delivery vehicle tracking
- Fleet management integration
- Operational efficiency monitoring

## 🔧 Development

### Running in Development Mode

```bash
export FLASK_ENV=development
export FLASK_DEBUG=1
python app.py
```

### Adding New Vehicle Types

Edit `detection_service.py` dan tambahkan class ID baru ke `VEHICLE_CLASSES`:

```python
VEHICLE_CLASSES = [
    1,   # bicycle
    2,   # car
    3,   # motorcycle
    5,   # bus
    7,   # truck
    # Add new vehicle class IDs here
]
```

### Creating Custom API Clients

Lihat folder `examples/` untuk template dan contoh implementasi client dalam berbagai bahasa pemrograman.

## Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/new-feature`)
3. Commit changes (`git commit -am 'Add new feature'`)
4. Push to branch (`git push origin feature/new-feature`)
5. Create Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support & Documentation

### 📚 Complete Documentation
- **[API Documentation](API_DOCUMENTATION.md)** - Complete REST API reference
- **[Integration Guide](API_INTEGRATION_GUIDE.md)** - Step-by-step integration guide
- **[Video Streaming Guide](VIDEO_STREAMING_API_GUIDE.md)** - Live video streaming API
- **[Integration Examples](STREAMING_INTEGRATION_EXAMPLES.md)** - Practical examples
- **[Project Overview](PROJECT_OVERVIEW.md)** - Detailed project overview

### 💻 Client Libraries
- **Python**: `examples/python/cctv_api_client.py`
- **JavaScript**: `examples/javascript/cctv-api-client.js`
- **PHP**: `examples/php/CCTVAPIClient.php`
- **HTML**: `examples/html/stream_viewer.html`

### 🚀 Quick Links
- **Web Interface**: `http://localhost:5000`
- **API Health Check**: `http://localhost:5000/api/v1/system/health`
- **API Documentation**: `http://localhost:5000/api/v1` (when running)
- **Stream Viewer**: `examples/html/stream_viewer.html`

## 🎉 Features Summary

✅ **Web-based CCTV monitoring** with real-time vehicle detection
✅ **REST API** for external integration and automation
✅ **Live video streaming** through API with detection overlay
✅ **Multi-platform clients** (Python, JavaScript, PHP, HTML)
✅ **Stream sharing** with public/private access control
✅ **Real-time notifications** via webhooks
✅ **Comprehensive documentation** and examples
✅ **Production-ready** with authentication and rate limiting

## 🙏 Acknowledgments

- [YOLOv5](https://github.com/ultralytics/yolov5) by Ultralytics - Object detection model
- [OpenCV](https://opencv.org/) - Computer vision library
- [Flask](https://flask.palletsprojects.com/) - Web framework
- [Bootstrap](https://getbootstrap.com/) - UI components
- [Axios](https://axios-http.com/) - HTTP client for JavaScript examples
