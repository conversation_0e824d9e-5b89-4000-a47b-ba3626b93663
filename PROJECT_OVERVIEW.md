# CCTV Monitoring System - Project Overview

## 🎯 Project Summary

Sistem pemantauan CCTV berbasis web yang telah berhasil dikembangkan dari sistem monitoring desktop menjadi aplikasi web yang dapat diakses melalui browser. Sistem ini memungkinkan pengguna untuk memasukkan URL stream kustom dan melihat hasil deteksi kendaraan secara real-time.

## ✅ Fitur yang Telah Diimplementasikan

### 🌐 Web Interface
- **Responsive Design**: Interface yang dapat diakses dari desktop dan mobile
- **Real-time Video Streaming**: Stream video langsung dengan overlay deteksi
- **Control Panel**: Panel kontrol untuk mengatur stream dan pengaturan deteksi
- **Statistics Dashboard**: Dashboard statistik real-time dengan breakdown per jenis kendaraan

### 🚗 Vehicle Detection
- **Multi-Vehicle Support**: Deteksi mobil, motor, bus, truk, dan sepeda
- **Real-time Processing**: Pemrosesan frame real-time dengan YOLOv5
- **Vehicle Tracking**: Sistem tracking untuk menghitung kendaraan unik
- **Configurable Settings**: Pengaturan confidence threshold dan filtering

### 🔧 Technical Features
- **Custom Stream URLs**: Support untuk HTTP, HTTPS, RTSP, M3U8
- **Error Handling**: Robust error handling untuk koneksi dan pemrosesan
- **Auto-reconnection**: Otomatis reconnect jika stream terputus
- **Performance Optimization**: Optimasi untuk streaming real-time

## 📁 Struktur Project

```
cctv/
├── 🌐 Web Application
│   ├── app.py                 # Main Flask application
│   ├── detection_service.py   # Detection and tracking logic
│   ├── templates/            # HTML templates
│   │   ├── base.html         # Base template
│   │   └── index.html        # Main interface
│   └── static/               # Static assets
│       ├── css/style.css     # Custom styling
│       └── js/main.js        # Frontend JavaScript
│
├── 🚀 Startup Scripts
│   ├── start_server.py       # Python startup script
│   ├── start_server.bat      # Windows batch file
│   └── start_server.sh       # Linux/macOS shell script
│
├── 🤖 AI Models
│   ├── yolov5s.pt           # Small YOLOv5 model
│   ├── yolov5m.pt           # Medium YOLOv5 model
│   └── yolov5l.pt           # Large YOLOv5 model
│
├── 📚 Documentation
│   ├── README.md            # Main documentation
│   ├── PROJECT_OVERVIEW.md  # This file
│   └── requirements.txt     # Python dependencies
│
└── 🔧 Legacy Files
    ├── index.py             # Original desktop application
    └── *.md                 # Development notes
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the Application

**Windows:**
```bash
start_server.bat
```

**Linux/macOS:**
```bash
./start_server.sh
```

**Manual:**
```bash
python app.py
```

### 3. Access Web Interface
- Open browser and go to `http://localhost:5000`
- Enter stream URL and click "Start Stream"
- Monitor real-time vehicle detection

## 🔧 Configuration

### Stream URL Examples
```
# HLS Stream (M3U8)
http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8

# RTSP Stream
rtsp://username:password@ip:port/stream

# HTTP Stream
http://example.com/live/stream.mp4
```

### Detection Settings
- **Confidence Threshold**: 0.05 - 0.9 (default: 0.25)
- **Strict Filtering**: Enable/disable false positive filtering
- **Motorcycle Mode**: Enhanced sensitivity for motorcycle detection

## 📊 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Main web interface |
| `/start_stream` | POST | Start stream monitoring |
| `/stop_stream` | POST | Stop stream monitoring |
| `/stream_stats` | GET | Get detection statistics |
| `/video_feed` | GET | Real-time video stream |

## 🔍 Vehicle Detection Classes

| Class ID | Vehicle Type | Color Code |
|----------|--------------|------------|
| 1 | Sepeda | Cyan |
| 2 | Mobil | Green |
| 3 | Motor | Red/Orange/Magenta |
| 5 | Bus | Blue |
| 7 | Truk | Yellow |

## 🎯 Key Improvements from Original

### From Desktop to Web
- ✅ **Web-based Interface**: Accessible from any browser
- ✅ **Custom Stream Input**: User can input any stream URL
- ✅ **Real-time Statistics**: Live dashboard with vehicle counts
- ✅ **Responsive Design**: Works on mobile and desktop
- ✅ **Better Error Handling**: Robust error management
- ✅ **Auto-reconnection**: Handles stream interruptions

### Enhanced Features
- ✅ **Multi-stream Support**: Architecture supports multiple streams
- ✅ **RESTful API**: Clean API for integration
- ✅ **Modern UI**: Bootstrap-based responsive interface
- ✅ **Real-time Updates**: AJAX-based live updates
- ✅ **Screenshot Capture**: Ability to capture frames
- ✅ **Fullscreen Mode**: Enhanced viewing experience

## 🔧 Technical Architecture

### Backend (Flask)
- **Framework**: Flask with threading support
- **AI/ML**: YOLOv5 for object detection
- **Computer Vision**: OpenCV for video processing
- **Streaming**: MJPEG over HTTP for real-time video

### Frontend
- **Framework**: Bootstrap 5 for responsive design
- **JavaScript**: jQuery for AJAX and DOM manipulation
- **Styling**: Custom CSS with modern design
- **Icons**: Font Awesome for UI icons

### Data Flow
1. User inputs stream URL via web interface
2. Flask validates and connects to stream
3. YOLOv5 processes frames for vehicle detection
4. Vehicle tracking system counts unique vehicles
5. Processed frames streamed to browser via MJPEG
6. Statistics updated in real-time via AJAX

## 🚀 Deployment Options

### Development
```bash
python app.py  # Debug mode on localhost:5000
```

### Production
```bash
# Using Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Using Docker (create Dockerfile)
docker build -t cctv-monitoring .
docker run -p 5000:5000 cctv-monitoring
```

## 🔮 Future Enhancements

### Planned Features
- [ ] **Multi-stream Dashboard**: Monitor multiple streams simultaneously
- [ ] **Database Integration**: Store detection history and analytics
- [ ] **User Authentication**: Login system for access control
- [ ] **Alert System**: Email/SMS notifications for specific events
- [ ] **Export Features**: Export statistics and video clips
- [ ] **Mobile App**: Native mobile application
- [ ] **Cloud Integration**: AWS/Azure deployment options

### Technical Improvements
- [ ] **WebRTC Support**: Lower latency streaming
- [ ] **GPU Optimization**: Better GPU utilization
- [ ] **Caching System**: Redis for session management
- [ ] **Load Balancing**: Support for multiple instances
- [ ] **Monitoring**: Health checks and metrics

## 📞 Support

### Common Issues
1. **Stream Connection**: Check URL format and network connectivity
2. **Performance**: Adjust model size and resolution for better performance
3. **Detection Accuracy**: Tune confidence threshold and filtering settings

### System Requirements
- **Minimum**: Python 3.8+, 4GB RAM, 2-core CPU
- **Recommended**: Python 3.9+, 8GB RAM, GPU with CUDA
- **Browser**: Modern browser with JavaScript enabled

## 🎉 Success Metrics

✅ **Functional Requirements Met:**
- Custom stream URL input ✓
- Real-time vehicle detection ✓
- Web-based interface ✓
- Statistics dashboard ✓
- Mobile responsive ✓

✅ **Technical Requirements Met:**
- Flask web framework ✓
- YOLOv5 integration ✓
- Real-time video streaming ✓
- Error handling ✓
- Documentation ✓

The CCTV Monitoring System has been successfully transformed from a desktop application to a modern web-based solution that can be easily deployed and accessed from anywhere!
