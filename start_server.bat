@echo off
title CCTV Monitoring System
color 0A

echo ============================================================
echo                🎥 CCTV Monitoring System
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "app.py" (
    echo ❌ Error: app.py not found!
    echo Please run this script from the CCTV project directory.
    pause
    exit /b 1
)

echo 📦 Checking Python installation...
python --version

echo.
echo 🚀 Starting CCTV Monitoring System...
echo    Press Ctrl+C to stop the server
echo    The web interface will open automatically
echo ============================================================
echo.

REM Start the Python application
python start_server.py

echo.
echo 👋 Server stopped
pause
