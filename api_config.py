"""
API Configuration for CCTV Monitoring System
============================================

Configuration settings for API authentication, rate limiting, and security.
"""

import os
import secrets
from datetime import timedelta

class APIConfig:
    """API Configuration class"""
    
    # API Security Settings
    SECRET_KEY = os.environ.get('API_SECRET_KEY', secrets.token_urlsafe(32))
    
    # Rate Limiting Settings
    RATE_LIMIT_STORAGE_URL = os.environ.get('REDIS_URL', 'memory://')
    DEFAULT_RATE_LIMIT = "100 per hour"
    
    # API Key Settings
    API_KEY_LENGTH = 32
    API_KEY_PREFIX = "cctv_"
    
    # Default API Keys (for development)
    DEFAULT_API_KEYS = {
        # Admin key with full permissions
        'admin': {
            'permissions': ['read', 'write', 'admin'],
            'description': 'Full admin access'
        },
        # Read-only key for monitoring
        'monitor': {
            'permissions': ['read'],
            'description': 'Read-only monitoring access'
        },
        # Integration key for external systems
        'integration': {
            'permissions': ['read', 'write'],
            'description': 'Integration access for external systems'
        }
    }
    
    # Webhook Settings
    WEBHOOK_TIMEOUT = 10  # seconds
    WEBHOOK_RETRY_ATTEMPTS = 3
    WEBHOOK_RETRY_DELAY = 5  # seconds
    
    # System Monitoring Settings
    SYSTEM_STATS_CACHE_TTL = 30  # seconds
    HEALTH_CHECK_INTERVAL = 60  # seconds
    
    # API Documentation Settings
    API_TITLE = "CCTV Monitoring API"
    API_VERSION = "1.0.0"
    API_DESCRIPTION = "REST API for CCTV monitoring and vehicle detection system"
    
    # CORS Settings
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '*').split(',')
    
    # Logging Settings
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    @classmethod
    def generate_default_keys(cls):
        """Generate default API keys for development"""
        keys = {}
        for key_name, config in cls.DEFAULT_API_KEYS.items():
            api_key = f"{cls.API_KEY_PREFIX}{key_name}_{secrets.token_urlsafe(cls.API_KEY_LENGTH)}"
            keys[api_key] = {
                'name': f"Default {key_name.title()} Key",
                'permissions': config['permissions'],
                'description': config['description'],
                'is_default': True
            }
        return keys

# API Response Templates
API_RESPONSES = {
    'success': {
        'success': True,
        'data': {},
        'message': 'Operation completed successfully'
    },
    'error': {
        'success': False,
        'error': 'Unknown error',
        'message': 'An error occurred'
    },
    'unauthorized': {
        'success': False,
        'error': 'Unauthorized',
        'message': 'Valid API key required'
    },
    'forbidden': {
        'success': False,
        'error': 'Forbidden',
        'message': 'Insufficient permissions'
    },
    'not_found': {
        'success': False,
        'error': 'Not found',
        'message': 'Resource not found'
    },
    'rate_limit': {
        'success': False,
        'error': 'Rate limit exceeded',
        'message': 'Too many requests'
    },
    'validation_error': {
        'success': False,
        'error': 'Validation error',
        'message': 'Invalid input data'
    }
}

# API Endpoint Documentation
API_ENDPOINTS = {
    'streams': {
        'GET /api/v1/streams': 'List all active streams',
        'POST /api/v1/streams': 'Create and start a new stream',
        'GET /api/v1/streams/{id}': 'Get specific stream information',
        'DELETE /api/v1/streams/{id}': 'Stop and delete a stream'
    },
    'detections': {
        'GET /api/v1/streams/{id}/detections': 'Get current detections for a stream',
        'GET /api/v1/streams/{id}/statistics': 'Get detailed statistics for a stream',
        'DELETE /api/v1/streams/{id}/statistics': 'Reset statistics for a stream'
    },
    'system': {
        'GET /api/v1/system/status': 'Get overall system status',
        'GET /api/v1/system/health': 'Simple health check (no auth required)'
    },
    'auth': {
        'GET /api/v1/auth/keys': 'List all API keys (admin only)',
        'POST /api/v1/auth/keys': 'Create new API key (admin only)'
    },
    'webhooks': {
        'GET /api/v1/webhooks': 'List registered webhooks',
        'POST /api/v1/webhooks': 'Register a webhook endpoint',
        'DELETE /api/v1/webhooks/{id}': 'Delete a webhook'
    }
}

# Vehicle Class Mappings
VEHICLE_CLASSES = {
    1: {'name': 'Sepeda', 'color': '#17a2b8', 'icon': 'bicycle'},
    2: {'name': 'Mobil', 'color': '#28a745', 'icon': 'car'},
    3: {'name': 'Motor', 'color': '#dc3545', 'icon': 'motorcycle'},
    5: {'name': 'Bus', 'color': '#007bff', 'icon': 'bus'},
    7: {'name': 'Truk', 'color': '#ffc107', 'icon': 'truck'}
}

# Webhook Event Types
WEBHOOK_EVENTS = {
    'vehicle_detected': 'Triggered when a vehicle is detected',
    'unique_vehicle_counted': 'Triggered when a unique vehicle is counted',
    'stream_started': 'Triggered when a stream is started',
    'stream_stopped': 'Triggered when a stream is stopped',
    'stream_error': 'Triggered when a stream encounters an error',
    'system_alert': 'Triggered for system-level alerts'
}

# API Usage Examples
API_EXAMPLES = {
    'create_stream': {
        'method': 'POST',
        'url': '/api/v1/streams',
        'headers': {
            'Content-Type': 'application/json',
            'X-API-Key': 'your-api-key-here'
        },
        'body': {
            'stream_url': 'http://example.com/stream.m3u8',
            'name': 'Main Entrance Camera'
        }
    },
    'get_detections': {
        'method': 'GET',
        'url': '/api/v1/streams/{stream_id}/detections',
        'headers': {
            'X-API-Key': 'your-api-key-here'
        }
    },
    'register_webhook': {
        'method': 'POST',
        'url': '/api/v1/webhooks',
        'headers': {
            'Content-Type': 'application/json',
            'X-API-Key': 'your-api-key-here'
        },
        'body': {
            'url': 'https://your-server.com/webhook',
            'events': ['vehicle_detected', 'unique_vehicle_counted']
        }
    }
}

# Error Codes and Messages
ERROR_CODES = {
    1001: 'Invalid API key format',
    1002: 'API key not found',
    1003: 'API key expired',
    1004: 'Insufficient permissions',
    1005: 'Rate limit exceeded',
    2001: 'Stream URL required',
    2002: 'Invalid stream URL format',
    2003: 'Stream connection failed',
    2004: 'Stream not found',
    2005: 'Stream already exists',
    3001: 'Webhook URL required',
    3002: 'Invalid webhook URL format',
    3003: 'Webhook registration failed',
    3004: 'Webhook not found',
    4001: 'System resource unavailable',
    4002: 'Database connection failed',
    4003: 'External service unavailable'
}

# Performance Monitoring
PERFORMANCE_METRICS = {
    'api_request_duration': 'Time taken to process API requests',
    'stream_processing_fps': 'Frames per second for stream processing',
    'detection_accuracy': 'Vehicle detection accuracy percentage',
    'system_resource_usage': 'CPU, memory, and GPU usage',
    'webhook_delivery_success': 'Webhook delivery success rate'
}

# Security Settings
SECURITY_SETTINGS = {
    'require_https': os.environ.get('REQUIRE_HTTPS', 'false').lower() == 'true',
    'api_key_rotation_days': int(os.environ.get('API_KEY_ROTATION_DAYS', '90')),
    'max_failed_attempts': int(os.environ.get('MAX_FAILED_ATTEMPTS', '5')),
    'lockout_duration_minutes': int(os.environ.get('LOCKOUT_DURATION', '15')),
    'allowed_ip_ranges': os.environ.get('ALLOWED_IP_RANGES', '').split(',') if os.environ.get('ALLOWED_IP_RANGES') else [],
    'enable_request_logging': os.environ.get('ENABLE_REQUEST_LOGGING', 'true').lower() == 'true'
}
