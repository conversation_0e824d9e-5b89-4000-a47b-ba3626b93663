# CCTV Video Streaming API - Complete Guide

## 🎯 Overview

Sistem CCTV Monitoring sekarang mendukung **live video streaming** dengan overlay deteksi melalui API. Project lain dapat melihat video real-time dengan hasil deteksi kendaraan, sama seperti di web interface.

## 🚀 Fitur Video Streaming API

### ✅ **Live Video Stream**
- **MJPEG Streaming** - Stream video real-time dengan format MJPEG
- **Detection Overlay** - Video sudah include overlay deteksi kendaraan
- **Real-time Processing** - Frame diproses secara real-time dengan YOLOv5
- **Multiple Formats** - Support untuk berbagai format akses

### ✅ **Stream Sharing**
- **Public Sharing** - Share stream untuk akses publik tanpa API key
- **Private Sharing** - Share dengan API key tertentu saja
- **Access Control** - Kontrol siapa yang bisa akses stream
- **Expiration** - Set waktu expired untuk sharing

### ✅ **Frame Capture**
- **Current Frame** - Ambil frame saat ini dalam format base64
- **Screenshot** - Capture frame untuk analisis
- **Metadata** - Include informasi deteksi dalam response

## 📡 API Endpoints untuk Video Streaming

### **1. Private Stream Access (Requires API Key)**

#### **Live Video Feed**
```http
GET /api/v1/streams/{stream_id}/video_feed
X-API-Key: your-api-key
```

**Response:** MJPEG video stream
```
Content-Type: multipart/x-mixed-replace; boundary=frame
X-Stream-ID: stream-id
X-Stream-URL: original-stream-url
```

#### **Current Frame**
```http
GET /api/v1/streams/{stream_id}/frame
X-API-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stream_id": "abc123",
    "frame_base64": "data:image/jpeg;base64,/9j/4AAQ...",
    "frame_count": 1500,
    "timestamp": "2024-08-01T15:30:00Z",
    "detections": [
      {
        "vehicle_id": 1,
        "class_id": 2,
        "confidence": 0.85,
        "bbox": {"x1": 100, "y1": 200, "x2": 300, "y2": 400}
      }
    ],
    "detection_count": 1
  }
}
```

#### **Stream Information**
```http
GET /api/v1/streams/{stream_id}/stream_info
X-API-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stream_id": "abc123",
    "capabilities": {
      "video_streaming": true,
      "frame_capture": true,
      "real_time_detection": true
    },
    "endpoints": {
      "video_feed": "http://localhost:5000/api/v1/streams/abc123/video_feed",
      "current_frame": "http://localhost:5000/api/v1/streams/abc123/frame"
    },
    "current_stats": {
      "unique_vehicles": 12,
      "currently_tracked": 3
    }
  }
}
```

### **2. Stream Sharing Management**

#### **Share Stream**
```http
POST /api/v1/streams/{stream_id}/share
X-API-Key: your-api-key
Content-Type: application/json

{
  "share_type": "public",
  "expires_at": "2024-08-02T00:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stream_id": "abc123",
    "share_type": "public",
    "share_urls": {
      "video_feed": "http://localhost:5000/api/v1/shared/streams/abc123/video_feed",
      "current_frame": "http://localhost:5000/api/v1/shared/streams/abc123/frame"
    },
    "expires_at": "2024-08-02T00:00:00Z"
  }
}
```

#### **Private Sharing**
```http
POST /api/v1/streams/{stream_id}/share
X-API-Key: your-api-key
Content-Type: application/json

{
  "share_type": "private",
  "allowed_keys": ["api-key-1", "api-key-2"],
  "expires_at": "2024-08-02T00:00:00Z"
}
```

#### **Stop Sharing**
```http
DELETE /api/v1/streams/{stream_id}/unshare
X-API-Key: your-api-key
```

### **3. Shared Stream Access (Public/Limited)**

#### **List Shared Streams**
```http
GET /api/v1/shared/streams
```

**Response:**
```json
{
  "success": true,
  "data": {
    "public_streams": [
      {
        "stream_id": "abc123",
        "share_type": "public",
        "created_at": "2024-08-01T10:00:00Z",
        "access_count": 25
      }
    ],
    "total_public": 1
  }
}
```

#### **Access Shared Video Feed**
```http
GET /api/v1/shared/streams/{stream_id}/video_feed
```

**Response:** MJPEG video stream with "SHARED STREAM" watermark

#### **Access Shared Frame**
```http
GET /api/v1/shared/streams/{stream_id}/frame
```

**Response:** Same as private frame but with watermark

## 💻 Implementation Examples

### **1. Python - OpenCV Display**

```python
import cv2
from examples.python.video_streaming_client import CCTVVideoStreamClient

# Initialize client
client = CCTVVideoStreamClient(
    'http://localhost:5000/api/v1',
    'your-api-key'
)

# Stream video with OpenCV
client.stream_video_opencv('your-stream-id', window_name="CCTV Live")
```

### **2. JavaScript - HTML Video Element**

```javascript
const streamUrl = 'http://localhost:5000/api/v1/streams/your-stream-id/video_feed?api_key=your-key';

// Create video element
const video = document.createElement('img');
video.src = streamUrl;
video.style.width = '100%';
document.body.appendChild(video);
```

### **3. HTML - Direct Embedding**

```html
<!DOCTYPE html>
<html>
<head>
    <title>CCTV Stream</title>
</head>
<body>
    <h1>Live CCTV Stream</h1>
    <img src="http://localhost:5000/api/v1/shared/streams/your-stream-id/video_feed" 
         style="width: 100%; max-width: 800px;" 
         alt="Live CCTV Stream">
</body>
</html>
```

### **4. PHP - Stream Processing**

```php
<?php
require_once 'examples/php/CCTVAPIClient.php';

$client = new CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key');

// Get current frame
$frameData = $client->getCurrentFrame('your-stream-id');
if ($frameData) {
    // Save frame
    $imageData = base64_decode($frameData['frame_base64']);
    file_put_contents('current_frame.jpg', $imageData);
    echo "Frame saved with {$frameData['detection_count']} detections\n";
}
?>
```

## 🔧 Advanced Usage

### **1. Multi-Stream Dashboard**

```python
# Monitor multiple streams simultaneously
stream_configs = [
    {'stream_id': 'entrance-cam', 'shared': False},
    {'stream_id': 'parking-cam', 'shared': True},
    {'stream_id': 'exit-cam', 'shared': False}
]

client.monitor_multiple_streams(stream_configs)
```

### **2. Frame Analysis Pipeline**

```python
import time
import cv2
import numpy as np

def analyze_frame(frame_data):
    """Custom frame analysis"""
    if frame_data['detections']:
        for detection in frame_data['detections']:
            vehicle_type = detection['vehicle_type']
            confidence = detection['confidence']
            print(f"Detected {vehicle_type} with {confidence:.2f} confidence")
    
    # Decode and process frame
    frame_bytes = base64.b64decode(frame_data['frame_base64'])
    nparr = np.frombuffer(frame_bytes, np.uint8)
    frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    
    # Your custom analysis here
    # e.g., traffic density analysis, anomaly detection, etc.
    
    return frame

# Continuous frame analysis
while True:
    frame_data = client.get_current_frame('your-stream-id')
    if frame_data:
        analyze_frame(frame_data)
    time.sleep(1)
```

### **3. Real-time Alert System**

```javascript
const client = new CCTVVideoStreamClient('http://localhost:5000/api/v1', 'your-api-key');

// Monitor for high traffic
const stopMonitoring = await client.monitorFrames('your-stream-id', 3000, false, (frameData) => {
    if (frameData.detections && frameData.detections.length > 5) {
        // Send alert
        sendAlert(`High traffic detected: ${frameData.detections.length} vehicles`);
    }
});
```

## 🌐 Integration Scenarios

### **1. Traffic Management System**

```python
# Integration with traffic light control
def traffic_control_integration():
    client = CCTVVideoStreamClient('http://localhost:5000/api/v1', 'traffic-api-key')
    
    while True:
        # Get current traffic
        frame_data = client.get_current_frame('intersection-cam')
        vehicle_count = len(frame_data.get('detections', []))
        
        # Adjust traffic light timing based on vehicle count
        if vehicle_count > 10:
            adjust_traffic_light('extend_green', 30)  # Extend green by 30 seconds
        elif vehicle_count < 2:
            adjust_traffic_light('reduce_green', 10)  # Reduce green by 10 seconds
        
        time.sleep(5)
```

### **2. Security Monitoring Dashboard**

```html
<!-- Real-time security dashboard -->
<div class="dashboard">
    <div class="stream-grid">
        <div class="stream-item">
            <h3>Main Entrance</h3>
            <img src="http://localhost:5000/api/v1/shared/streams/entrance/video_feed">
        </div>
        <div class="stream-item">
            <h3>Parking Area</h3>
            <img src="http://localhost:5000/api/v1/shared/streams/parking/video_feed">
        </div>
        <div class="stream-item">
            <h3>Exit Gate</h3>
            <img src="http://localhost:5000/api/v1/shared/streams/exit/video_feed">
        </div>
    </div>
</div>
```

### **3. Mobile App Integration**

```javascript
// React Native example
import React, { useState, useEffect } from 'react';
import { Image, View, Text } from 'react-native';

const CCTVStreamView = ({ streamId, apiKey }) => {
    const [streamUrl, setStreamUrl] = useState('');
    const [stats, setStats] = useState({});
    
    useEffect(() => {
        const url = `http://localhost:5000/api/v1/streams/${streamId}/video_feed?api_key=${apiKey}`;
        setStreamUrl(url);
        
        // Update stats every 5 seconds
        const interval = setInterval(async () => {
            const response = await fetch(`http://localhost:5000/api/v1/streams/${streamId}/stream_info`, {
                headers: { 'X-API-Key': apiKey }
            });
            const data = await response.json();
            if (data.success) {
                setStats(data.data.current_stats);
            }
        }, 5000);
        
        return () => clearInterval(interval);
    }, [streamId, apiKey]);
    
    return (
        <View>
            <Image source={{ uri: streamUrl }} style={{ width: '100%', height: 300 }} />
            <Text>Unique Vehicles: {stats.unique_vehicles || 0}</Text>
            <Text>Currently Tracked: {stats.currently_tracked || 0}</Text>
        </View>
    );
};
```

## 🔒 Security & Performance

### **Access Control**
- **API Key Authentication** - Required for private streams
- **Rate Limiting** - 50 requests/hour for video feeds
- **Shared Stream Control** - Public/private sharing options
- **Expiration Management** - Auto-expire shared streams

### **Performance Optimization**
- **MJPEG Compression** - Optimized for streaming
- **Frame Rate Control** - ~30 FPS for smooth playback
- **Bandwidth Management** - Adjustable quality settings
- **Connection Pooling** - Efficient resource usage

### **Monitoring**
- **Stream Health** - Monitor connection status
- **Access Logs** - Track stream access
- **Performance Metrics** - Frame rate, latency monitoring
- **Error Handling** - Automatic reconnection

## 🎉 Success!

Sistem CCTV Monitoring sekarang mendukung **live video streaming** melalui API! Project lain dapat:

✅ **Melihat Live Stream** - Video real-time dengan overlay deteksi
✅ **Akses Frame** - Capture frame saat ini dengan metadata deteksi  
✅ **Share Stream** - Berbagi stream secara public atau private
✅ **Multi-Platform** - Support Python, JavaScript, PHP, HTML
✅ **Real-time Processing** - Deteksi kendaraan real-time
✅ **Secure Access** - API key authentication dan access control

**Use Cases:**
- 🚦 Traffic management systems
- 🏢 Security monitoring dashboards  
- 📱 Mobile surveillance apps
- 🌐 Web-based monitoring portals
- 📊 Analytics and reporting systems

Project lain sekarang bisa mengintegrasikan live video monitoring dengan mudah!
