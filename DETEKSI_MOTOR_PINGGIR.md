# Deteksi Motor di Pinggir Frame

## Ma<PERSON>ah yang Ditemukan

Motor yang berada di pinggir frame atau area marginal sering tidak terdeteksi karena:

1. **Berada di luar ROI standar** - Area pinggir dianggap bukan zona kendaraan
2. **Confidence sangat rendah** - Motor jauh atau sebagian terpotong frame
3. **Ukuran sangat kecil** - Motor di kejauhan atau terpotong
4. **Aspect ratio aneh** - Motor terpotong oleh batas frame

## Algoritma Deteksi Motor Pinggir

### Identifikasi Area Pinggir
```python
# Area pinggir kiri: 0-15% dari lebar frame
# Area pinggir kanan: 85-100% dari lebar frame  
# Area pinggir atas: 0-20% dari tinggi frame
# Area pinggir bawah: 80-100% dari tinggi frame
```

### Kriteria Motor Pinggir
**Edge Score dihitung berdasarkan:**

1. **Posisi Pinggir** (+1 poin)
   - Kiri: center_x < 15% frame width
   - Kanan: center_x > 85% frame width
   - Atas: center_y < 20% frame height  
   - Bawah: center_y > 80% frame height

2. **Ukuran Sangat Kecil** (+1 poin)
   - Area relatif < 0.5% dari total frame

3. **Aspect Ratio Aneh** (+1 poin)
   - Ratio < 0.5 atau > 3.0 (kemungkinan terpotong)

### Validasi dan Boost
```python
if is_edge_position and edge_score >= 2:
    confidence_boost = edge_score * 0.05  # Max +0.25
    adjusted_conf = min(0.4, original_conf + boost)
```

## Parameter Deteksi

### Confidence Range Motor Pinggir
```python
# Motor pinggir: 0.05 - 0.2 (sangat rendah)
# Boost maksimal: +0.25 (hingga 0.4)
```

### ROI Diperluas
```python
# Sebelum: 10% pinggir diabaikan
# Sesudah: 5% pinggir diabaikan (area deteksi lebih luas)
```

### Minimum Bounding Box
```python
if width < 25: expand_to_25px
if height < 25: expand_to_25px
```

## Visualisasi 4 Jenis Motor

- 🔴 **Motor** (Merah): Normal, confidence > 0.4
- 🟠 **Motor*** (Orange): Berjajar, confidence 0.25-0.4
- 🟡 **Motor#** (Kuning): Tertutup, confidence 0.15-0.25
- 🔵 **Motor~** (Cyan): Pinggir, confidence 0.1-0.15

## Contoh Skenario

### Skenario 1: Motor di Pinggir Kiri
```
Input:
- Motor: conf=0.12, center_x=8% (pinggir kiri)
- Area: 0.3% (sangat kecil)
- Ratio: 0.4 (terpotong)

Analisis:
- Posisi pinggir: +1 poin
- Ukuran kecil: +1 poin  
- Ratio aneh: +1 poin
- Total: 3 poin

Output:
- Confidence: 0.12 + (3×0.05) = 0.27
- Status: DETECTED sebagai "Motor~"
```

### Skenario 2: Motor di Tengah Frame
```
Input:
- Motor: conf=0.12, center_x=50% (tengah)
- Area: 0.3% (kecil)

Analisis:
- Posisi pinggir: 0 poin
- Total: 1 poin (tidak cukup)

Output:
- Status: REJECTED
```

## Integrasi dengan Sistem

### Urutan Deteksi
```
1. Deteksi YOLOv5 standar
2. + Deteksi motor berjajar
3. + Deteksi motor tertutup  
4. + Deteksi motor pinggir
5. → Validasi final
```

### Threshold Confidence Bertingkat
```python
if class_id == 3:  # Motor
    confidence_ok = conf > 0.1  # Sangat rendah
else:
    confidence_ok = conf > 0.3  # Normal
```

## Monitoring dan Debug

### Display Info
```
ROI: Cyan=zona | Motor*=berjajar | Motor#=tertutup | Motor~=pinggir
Motor berjajar: 2
Motor tertutup: 1  
Motor pinggir: 1
```

### Console Debug (tekan 'd')
```
=== DEBUG FRAME 123 ===
DETECTED: Motor~ | Conf: 0.27 | Area: 0.3% | Ratio: 0.4 | Pos: (100,400) | Size_OK: True | ROI_OK: False
MISSED: Motor | Conf: 0.08 | Area: 0.2% | Ratio: 5.0 | Pos: (50,350) | Size_OK: False | ROI_OK: False
```

## Tips Optimasi

### 1. Jika Motor Pinggir Masih Terlewat
```python
# Turunkan threshold confidence
if class_id == 3:
    confidence_ok = conf > 0.08  # Dari 0.1 ke 0.08
```

### 2. Jika Terlalu Banyak False Positive
```python
# Naikkan minimum edge score
if is_edge_position and edge_score >= 3:  # Dari 2 ke 3
```

### 3. Sesuaikan Area Pinggir
```python
# Untuk motor di pinggir yang lebih jauh
if center_x < frame_width * 0.2:  # Dari 0.15 ke 0.2
```

## Hasil yang Diharapkan

✅ **Motor di pinggir kiri/kanan terdeteksi**
✅ **Motor sebagian keluar frame terdeteksi**
✅ **Motor parkir di pinggir jalan terdeteksi**
✅ **Visualisasi jelas dengan warna cyan**
✅ **Counter khusus motor pinggir**
✅ **ROI diperluas untuk coverage lebih baik**

## Limitasi

⚠️ **Motor 95%+ keluar frame tidak terdeteksi**
⚠️ **Objek kecil di pinggir bisa false positive**
⚠️ **Perlu penyesuaian per sudut kamera**

## Summary Sistem Lengkap

Sekarang sistem dapat mendeteksi **4 jenis motor**:

1. **Motor** (🔴 Merah) - Normal, jelas terlihat
2. **Motor*** (🟠 Orange) - Berjajar dengan motor lain
3. **Motor#** (🟡 Kuning) - Tertutup kendaraan/objek lain
4. **Motor~** (🔵 Cyan) - Di pinggir frame/area marginal

**Total coverage**: Motor normal + berjajar + tertutup + pinggir = Deteksi maksimal!
