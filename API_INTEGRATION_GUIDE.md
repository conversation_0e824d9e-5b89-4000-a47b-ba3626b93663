# CCTV Monitoring System - API Integration Guide

## 🎯 Overview

Sistem CCTV Monitoring sekarang dilengkapi dengan REST API yang komprehensif yang memungkinkan project lain untuk memonitoring dan mengintegrasikan dengan sistem deteksi kendaraan secara programmatik.

## 🚀 Quick Start

### 1. Start the System

```bash
python app.py
```

The system will generate an admin API key automatically. Look for this line in the console:
```
INFO:api:Generated admin API key: cctv_admin_[your-key-here]
```

### 2. Test API Health

```bash
curl http://localhost:5000/api/v1/system/health
```

Expected response:
```json
{
    "status": "healthy",
    "timestamp": "2024-08-01T10:30:00Z",
    "version": "1.0.0"
}
```

### 3. Create Your First Stream

```bash
curl -X POST http://localhost:5000/api/v1/streams \
  -H "X-API-Key: cctv_admin_[your-key-here]" \
  -H "Content-Type: application/json" \
  -d '{
    "stream_url": "http://stream.cctv.malangkota.go.id/WebRTCApp/streams/697348239578014654770613.m3u8",
    "name": "Test Stream"
  }'
```

## 📡 API Endpoints

### Base URL
```
http://localhost:5000/api/v1
```

### Authentication
Include your API key in the header:
```
X-API-Key: your-api-key-here
```

### Core Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/system/health` | Health check (no auth) | None |
| GET | `/system/status` | System status | Read |
| GET | `/streams` | List all streams | Read |
| POST | `/streams` | Create new stream | Write |
| GET | `/streams/{id}` | Get stream details | Read |
| DELETE | `/streams/{id}` | Delete stream | Write |
| GET | `/streams/{id}/detections` | Get current detections | Read |
| GET | `/streams/{id}/statistics` | Get stream statistics | Read |
| DELETE | `/streams/{id}/statistics` | Reset statistics | Write |
| GET | `/webhooks` | List webhooks | Read |
| POST | `/webhooks` | Register webhook | Write |
| DELETE | `/webhooks/{id}` | Delete webhook | Write |
| GET | `/auth/keys` | List API keys | Admin |
| POST | `/auth/keys` | Create API key | Admin |

## 🔑 API Key Management

### Get Admin Key
The admin key is automatically generated when the system starts. Check the console output.

### Create New API Keys

```bash
curl -X POST http://localhost:5000/api/v1/auth/keys \
  -H "X-API-Key: cctv_admin_[admin-key]" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Integration Key",
    "permissions": ["read", "write"]
  }'
```

### Permission Levels
- **read**: View streams, detections, and statistics
- **write**: Create/delete streams, manage webhooks, reset statistics
- **admin**: Manage API keys and system settings

## 🔔 Webhook Integration

### Register Webhook

```bash
curl -X POST http://localhost:5000/api/v1/webhooks \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://your-server.com/webhook",
    "events": ["vehicle_detected", "unique_vehicle_counted", "stream_started"]
  }'
```

### Webhook Events

| Event | Description | Payload |
|-------|-------------|---------|
| `vehicle_detected` | Vehicle detected in stream | Vehicle details, bbox, confidence |
| `unique_vehicle_counted` | Unique vehicle counted | Vehicle type, total count |
| `stream_started` | Stream started | Stream ID, URL |
| `stream_stopped` | Stream stopped | Stream ID, URL |

### Example Webhook Payload

```json
{
    "event_type": "vehicle_detected",
    "timestamp": "2024-08-01T10:30:00Z",
    "data": {
        "stream_id": "uuid-here",
        "vehicle_type": "Mobil",
        "class_id": 2,
        "confidence": 0.85,
        "bbox": {
            "x1": 100.5,
            "y1": 200.3,
            "x2": 300.7,
            "y2": 400.9
        }
    }
}
```

## 💻 Client Examples

### Python

```python
from examples.python.cctv_api_client import CCTVAPIClient

client = CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key')

# Create stream
stream = client.create_stream('http://example.com/stream.m3u8')
stream_id = stream['stream_id']

# Get detections
detections = client.get_detections(stream_id)
print(f"Found {len(detections)} vehicles")

# Get statistics
stats = client.get_statistics(stream_id)
print(f"Unique vehicles: {stats['tracking_stats']['total_unique']}")
```

### JavaScript/Node.js

```javascript
const { CCTVAPIClient } = require('./examples/javascript/cctv-api-client');

const client = new CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key');

// Create stream
const stream = await client.createStream('http://example.com/stream.m3u8');
const streamId = stream.stream_id;

// Get detections
const detections = await client.getDetections(streamId);
console.log(`Found ${detections.length} vehicles`);

// Monitor continuously
await monitorStreamContinuously(client, streamId, 300); // 5 minutes
```

### PHP

```php
require_once 'examples/php/CCTVAPIClient.php';

$client = new CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key');

// Create stream
$stream = $client->createStream('http://example.com/stream.m3u8');
$streamId = $stream['stream_id'];

// Get detections
$detections = $client->getDetections($streamId);
echo "Found " . count($detections) . " vehicles\n";
```

## 📊 Real-time Monitoring

### Continuous Monitoring

```python
# Monitor stream for 5 minutes
monitor_stream_continuously(client, stream_id, duration=300)
```

### Webhook Listener

```javascript
// Start webhook listener
startWebhookListener(3000);

// Register webhook
const webhook = await client.registerWebhook(
    'http://localhost:3000/webhook',
    ['vehicle_detected', 'unique_vehicle_counted']
);
```

## 🔧 Integration Patterns

### 1. Real-time Dashboard

```python
# Get system overview
status = client.get_system_status()
print(f"Active streams: {status['streams']['active']}")
print(f"Total detections: {status['detections']['total_detections']}")

# Get all streams with their statistics
streams = client.list_streams()
for stream in streams:
    stats = client.get_statistics(stream['stream_id'])
    print(f"Stream {stream['stream_id']}: {stats['tracking_stats']['total_unique']} unique vehicles")
```

### 2. Batch Processing

```python
# Create multiple streams
stream_urls = [
    'http://stream1.com/feed.m3u8',
    'http://stream2.com/feed.m3u8',
    'http://stream3.com/feed.m3u8'
]

stream_ids = batch_create_streams(client, stream_urls)

# Monitor all streams
for stream_id in stream_ids:
    detections = client.get_detections(stream_id)
    print(f"Stream {stream_id}: {len(detections)} current detections")
```

### 3. Data Export

```python
# Export statistics to CSV
export_statistics_to_csv(client, stream_id, 'vehicle_stats.csv')

# Export to JSON
export_statistics_to_json(client, stream_id, 'vehicle_stats.json')
```

## 🚨 Error Handling

### Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 401 | Unauthorized | Check API key |
| 403 | Forbidden | Check permissions |
| 404 | Not Found | Verify stream ID |
| 429 | Rate Limited | Reduce request frequency |
| 500 | Server Error | Check system logs |

### Example Error Response

```json
{
    "success": false,
    "error": "Stream not found",
    "message": "Stream with ID abc123 does not exist",
    "timestamp": "2024-08-01T10:30:00Z"
}
```

## 📈 Performance Tips

1. **Use appropriate permissions** - Use read-only keys when possible
2. **Cache responses** - Cache system status and stream lists
3. **Batch operations** - Create multiple streams in batch
4. **Monitor rate limits** - Check rate limit headers
5. **Use webhooks** - For real-time updates instead of polling

## 🔒 Security Best Practices

1. **Secure API keys** - Never expose keys in client-side code
2. **Use HTTPS** - Always use HTTPS in production
3. **Rotate keys** - Regularly rotate API keys
4. **Monitor usage** - Track API key usage
5. **Validate inputs** - Always validate user inputs

## 🐛 Troubleshooting

### API Not Responding
```bash
# Check if service is running
curl http://localhost:5000/api/v1/system/health

# Check logs
tail -f app.log
```

### Authentication Issues
```bash
# Test with admin key
curl -H "X-API-Key: cctv_admin_[key]" http://localhost:5000/api/v1/auth/keys
```

### Stream Connection Issues
```bash
# Test stream URL directly
curl -I "http://your-stream-url.com/stream.m3u8"

# Check stream validation
curl -X POST http://localhost:5000/api/v1/streams \
  -H "X-API-Key: your-key" \
  -H "Content-Type: application/json" \
  -d '{"stream_url": "your-url"}'
```

## 📞 Support

For API support:
1. Check the API documentation
2. Review example code
3. Test with health check endpoint
4. Verify API key permissions
5. Check system logs for errors

## 🎉 Success!

Your CCTV Monitoring System now has a complete REST API that allows:

✅ **External Integration** - Other projects can monitor streams programmatically
✅ **Real-time Notifications** - Webhook support for instant updates  
✅ **Scalable Architecture** - Support for multiple streams and clients
✅ **Comprehensive Documentation** - Complete API docs and examples
✅ **Multi-language Support** - Client examples in Python, JavaScript, PHP
✅ **Security** - API key authentication with permission levels
✅ **Monitoring** - System status and health check endpoints

The system is now ready for production use and can be easily integrated with other applications, dashboards, and monitoring systems!
