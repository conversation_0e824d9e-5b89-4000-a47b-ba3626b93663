<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCTV Stream Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            padding: 20px;
        }

        .stream-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .stream-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .stream-video {
            width: 100%;
            height: auto;
            display: block;
        }

        .stream-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }

        .stream-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn.danger {
            background: #dc3545;
        }

        .btn.danger:hover {
            background: #c82333;
        }

        .btn.success {
            background: #28a745;
        }

        .btn.success:hover {
            background: #218838;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .info-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }

        .info-card h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-offline {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .config-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #007bff;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .log-container {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid #007bff;
            background: white;
            border-radius: 3px;
        }

        .log-entry.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .log-entry.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stream-controls {
                justify-content: center;
            }
            
            .btn {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 CCTV Stream Viewer</h1>
            <p>Real-time Vehicle Detection Monitoring</p>
        </div>

        <div class="main-content">
            <!-- Main Stream Section -->
            <div class="stream-section">
                <div class="stream-container">
                    <img id="streamVideo" class="stream-video" alt="CCTV Stream" style="display: none;">
                    <div id="streamPlaceholder" style="height: 400px; display: flex; align-items: center; justify-content: center; color: #6c757d;">
                        <div style="text-align: center;">
                            <div style="font-size: 4em; margin-bottom: 20px;">📹</div>
                            <h3>No Stream Selected</h3>
                            <p>Configure stream settings and click "Start Stream"</p>
                        </div>
                    </div>
                    <div class="stream-overlay" id="streamOverlay" style="display: none;">
                        <div>🔴 LIVE</div>
                        <div id="streamInfo">Stream: Loading...</div>
                    </div>
                </div>

                <div class="stream-controls">
                    <button class="btn success" onclick="startStream()">
                        ▶️ Start Stream
                    </button>
                    <button class="btn danger" onclick="stopStream()">
                        ⏹️ Stop Stream
                    </button>
                    <button class="btn" onclick="refreshStream()">
                        🔄 Refresh
                    </button>
                    <button class="btn" onclick="takeScreenshot()">
                        📸 Screenshot
                    </button>
                    <button class="btn" onclick="toggleFullscreen()">
                        🔍 Fullscreen
                    </button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Configuration -->
                <div class="config-section">
                    <h3>⚙️ Stream Configuration</h3>
                    
                    <div class="form-group">
                        <label for="apiUrl">API Base URL:</label>
                        <input type="text" id="apiUrl" value="http://localhost:5000/api/v1" placeholder="http://localhost:5000/api/v1">
                    </div>
                    
                    <div class="form-group">
                        <label for="apiKey">API Key:</label>
                        <input type="password" id="apiKey" placeholder="Enter your API key">
                    </div>
                    
                    <div class="form-group">
                        <label for="streamId">Stream ID:</label>
                        <input type="text" id="streamId" placeholder="Enter stream ID">
                    </div>
                    
                    <div class="form-group">
                        <label for="streamType">Stream Type:</label>
                        <select id="streamType">
                            <option value="private">Private Stream</option>
                            <option value="shared">Shared Stream</option>
                        </select>
                    </div>
                    
                    <button class="btn" onclick="loadSharedStreams()" style="width: 100%; margin-top: 10px;">
                        📋 Load Shared Streams
                    </button>
                </div>

                <!-- Status -->
                <div class="info-card">
                    <h3>📊 Stream Status</h3>
                    <p>
                        <span class="status-indicator status-offline" id="statusIndicator"></span>
                        <span id="streamStatus">Disconnected</span>
                    </p>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="frameCount">0</div>
                            <div class="stat-label">Frames</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="detectionCount">0</div>
                            <div class="stat-label">Detections</div>
                        </div>
                    </div>
                </div>

                <!-- Detection Stats -->
                <div class="info-card">
                    <h3>🚗 Vehicle Statistics</h3>
                    <div id="vehicleStats">
                        <div class="stat-item">
                            <div class="stat-value" id="uniqueVehicles">0</div>
                            <div class="stat-label">Unique Vehicles</div>
                        </div>
                        <div style="margin-top: 10px; font-size: 14px;">
                            <div>🚗 Cars: <span id="carCount">0</span></div>
                            <div>🏍️ Motorcycles: <span id="motorcycleCount">0</span></div>
                            <div>🚌 Buses: <span id="busCount">0</span></div>
                            <div>🚛 Trucks: <span id="truckCount">0</span></div>
                            <div>🚲 Bicycles: <span id="bicycleCount">0</span></div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="info-card">
                    <h3>📝 Activity Log</h3>
                    <div class="log-container" id="activityLog">
                        <div class="log-entry">System ready</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStreamUrl = null;
        let statsInterval = null;
        let isStreaming = false;

        // Add log entry
        function addLog(message, type = 'info') {
            const log = document.getElementById('activityLog');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.insertBefore(entry, log.firstChild);
            
            // Keep only last 20 entries
            while (log.children.length > 20) {
                log.removeChild(log.lastChild);
            }
        }

        // Update status
        function updateStatus(status, online = false) {
            document.getElementById('streamStatus').textContent = status;
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${online ? 'status-online' : 'status-offline'}`;
        }

        // Start stream
        function startStream() {
            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const streamId = document.getElementById('streamId').value;
            const streamType = document.getElementById('streamType').value;

            if (!streamId) {
                addLog('Please enter a stream ID', 'error');
                return;
            }

            const endpoint = streamType === 'shared' 
                ? `/shared/streams/${streamId}/video_feed`
                : `/streams/${streamId}/video_feed`;
            
            let streamUrl = `${apiUrl}${endpoint}`;
            
            // Add API key for private streams
            if (streamType === 'private' && apiKey) {
                streamUrl += `?api_key=${apiKey}`;
            }

            currentStreamUrl = streamUrl;
            
            const video = document.getElementById('streamVideo');
            const placeholder = document.getElementById('streamPlaceholder');
            const overlay = document.getElementById('streamOverlay');
            
            video.src = streamUrl;
            video.style.display = 'block';
            placeholder.style.display = 'none';
            overlay.style.display = 'block';
            
            document.getElementById('streamInfo').textContent = `Stream: ${streamId}`;
            
            isStreaming = true;
            updateStatus('Connecting...', false);
            addLog(`Starting stream: ${streamId}`, 'info');
            
            // Start stats updates
            if (statsInterval) clearInterval(statsInterval);
            statsInterval = setInterval(updateStats, 5000);
        }

        // Stop stream
        function stopStream() {
            const video = document.getElementById('streamVideo');
            const placeholder = document.getElementById('streamPlaceholder');
            const overlay = document.getElementById('streamOverlay');
            
            video.src = '';
            video.style.display = 'none';
            placeholder.style.display = 'flex';
            overlay.style.display = 'none';
            
            currentStreamUrl = null;
            isStreaming = false;
            updateStatus('Disconnected', false);
            addLog('Stream stopped', 'info');
            
            // Stop stats updates
            if (statsInterval) {
                clearInterval(statsInterval);
                statsInterval = null;
            }
        }

        // Refresh stream
        function refreshStream() {
            if (currentStreamUrl) {
                const video = document.getElementById('streamVideo');
                const currentSrc = video.src;
                video.src = '';
                setTimeout(() => {
                    video.src = currentSrc + (currentSrc.includes('?') ? '&' : '?') + 't=' + Date.now();
                }, 100);
                addLog('Stream refreshed', 'info');
            }
        }

        // Take screenshot
        function takeScreenshot() {
            const video = document.getElementById('streamVideo');
            if (!video.src) {
                addLog('No active stream to capture', 'error');
                return;
            }

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = video.naturalWidth || video.width;
            canvas.height = video.naturalHeight || video.height;
            
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            const link = document.createElement('a');
            link.download = `cctv_screenshot_${Date.now()}.png`;
            link.href = canvas.toDataURL();
            link.click();
            
            addLog('Screenshot saved', 'success');
        }

        // Toggle fullscreen
        function toggleFullscreen() {
            const video = document.getElementById('streamVideo');
            if (!document.fullscreenElement) {
                video.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // Update statistics
        async function updateStats() {
            if (!isStreaming) return;

            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const streamId = document.getElementById('streamId').value;
            const streamType = document.getElementById('streamType').value;

            try {
                const endpoint = streamType === 'shared' 
                    ? `/shared/streams/${streamId}/info`
                    : `/streams/${streamId}/stream_info`;

                const headers = {};
                if (streamType === 'private' && apiKey) {
                    headers['X-API-Key'] = apiKey;
                }

                const response = await fetch(`${apiUrl}${endpoint}`, { headers });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const stats = data.data.current_stats || {};
                        
                        document.getElementById('uniqueVehicles').textContent = stats.unique_vehicles || 0;
                        document.getElementById('carCount').textContent = stats.by_type?.Mobil || 0;
                        document.getElementById('motorcycleCount').textContent = stats.by_type?.Motor || 0;
                        document.getElementById('busCount').textContent = stats.by_type?.Bus || 0;
                        document.getElementById('truckCount').textContent = stats.by_type?.Truk || 0;
                        document.getElementById('bicycleCount').textContent = stats.by_type?.Sepeda || 0;
                        
                        updateStatus('Connected', true);
                    }
                }
            } catch (error) {
                console.error('Error updating stats:', error);
                updateStatus('Error', false);
            }
        }

        // Load shared streams
        async function loadSharedStreams() {
            const apiUrl = document.getElementById('apiUrl').value;
            
            try {
                const response = await fetch(`${apiUrl}/shared/streams`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.public_streams.length > 0) {
                        const firstStream = data.data.public_streams[0];
                        document.getElementById('streamId').value = firstStream.stream_id;
                        document.getElementById('streamType').value = 'shared';
                        addLog(`Loaded shared stream: ${firstStream.stream_id}`, 'success');
                    } else {
                        addLog('No public shared streams available', 'error');
                    }
                }
            } catch (error) {
                addLog('Error loading shared streams', 'error');
            }
        }

        // Video event handlers
        document.getElementById('streamVideo').onload = () => {
            updateStatus('Connected', true);
            addLog('Stream connected successfully', 'success');
        };

        document.getElementById('streamVideo').onerror = () => {
            updateStatus('Connection failed', false);
            addLog('Stream connection failed', 'error');
        };

        // Initialize
        addLog('CCTV Stream Viewer initialized', 'success');
    </script>
</body>
</html>
