# CCTV Monitoring API - Client Examples

This directory contains example implementations of CCTV Monitoring API clients in various programming languages.

## Available Examples

### 🐍 Python (`python/cctv_api_client.py`)

**Requirements:**
```bash
pip install requests
```

**Usage:**
```python
from cctv_api_client import CCTVAPIClient

client = CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key')
streams = client.list_streams()
```

**Features:**
- Complete API client implementation
- Utility functions for monitoring and batch operations
- CSV export functionality
- Error handling and logging

### 🟨 JavaScript/Node.js (`javascript/cctv-api-client.js`)

**Requirements:**
```bash
npm install axios express
```

**Usage:**
```javascript
const { CCTVAPIClient } = require('./cctv-api-client');

const client = new CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key');
const streams = await client.listStreams();
```

**Features:**
- Promise-based API client
- Express.js webhook listener
- Real-time monitoring functions
- JSON export functionality

### 🐘 PHP (`php/CCTVAPIClient.php`)

**Requirements:**
- PHP 7.0+ with cURL extension

**Usage:**
```php
require_once 'CCTVAPIClient.php';

$client = new CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key');
$streams = $client->listStreams();
```

**Features:**
- Object-oriented API client
- Built-in error handling
- Webhook receiver example
- JSON export functionality

## Quick Start Guide

### 1. Get API Key

First, you need to obtain an API key from the CCTV system administrator or generate one using the admin endpoints.

### 2. Basic Operations

All examples demonstrate these basic operations:

1. **Health Check** - Verify system is running
2. **Create Stream** - Start monitoring a new CCTV stream
3. **Get Detections** - Retrieve current vehicle detections
4. **Get Statistics** - Get detection statistics and counts
5. **Manage Webhooks** - Register for real-time notifications
6. **Clean Up** - Stop and delete streams

### 3. Running Examples

**Python:**
```bash
cd examples/python
python cctv_api_client.py
```

**JavaScript:**
```bash
cd examples/javascript
npm install axios
node cctv-api-client.js
```

**PHP:**
```bash
cd examples/php
php CCTVAPIClient.php
```

## Common Use Cases

### Real-time Monitoring

Monitor a stream continuously and get notified of changes:

**Python:**
```python
monitor_stream_continuously(client, stream_id, duration=300)  # 5 minutes
```

**JavaScript:**
```javascript
await monitorStreamContinuously(client, streamId, 300);
```

**PHP:**
```php
monitorStreamContinuously($client, $streamId, 300);
```

### Batch Stream Management

Create multiple streams at once:

**Python:**
```python
urls = ['http://stream1.com', 'http://stream2.com']
stream_ids = batch_create_streams(client, urls)
```

**JavaScript:**
```javascript
const urls = ['http://stream1.com', 'http://stream2.com'];
const streamIds = await batchCreateStreams(client, urls);
```

**PHP:**
```php
$urls = ['http://stream1.com', 'http://stream2.com'];
$streamIds = batchCreateStreams($client, $urls);
```

### Webhook Integration

Set up real-time notifications:

**Python:**
```python
webhook = client.register_webhook(
    'https://your-server.com/webhook',
    ['vehicle_detected', 'unique_vehicle_counted']
)
```

**JavaScript:**
```javascript
// Start webhook listener
startWebhookListener(3000);

// Register webhook
const webhook = await client.registerWebhook(
    'http://localhost:3000/webhook',
    ['vehicle_detected', 'unique_vehicle_counted']
);
```

**PHP:**
```php
$webhook = $client->registerWebhook(
    'https://your-server.com/webhook',
    ['vehicle_detected', 'unique_vehicle_counted']
);
```

### Data Export

Export statistics for analysis:

**Python:**
```python
export_statistics_to_csv(client, stream_id, 'stats.csv')
```

**JavaScript:**
```javascript
await exportStatisticsToJSON(client, streamId, 'stats.json');
```

**PHP:**
```php
exportStatisticsToJSON($client, $streamId, 'stats.json');
```

## Error Handling

All examples include comprehensive error handling:

- **Network errors** - Connection timeouts, DNS failures
- **API errors** - Invalid requests, authentication failures
- **Rate limiting** - Automatic retry with exponential backoff
- **Data validation** - Input validation and sanitization

## Configuration

### Environment Variables

You can configure the examples using environment variables:

```bash
export CCTV_API_URL="http://localhost:5000/api/v1"
export CCTV_API_KEY="your-api-key-here"
export WEBHOOK_URL="https://your-server.com/webhook"
```

### Configuration Files

Create a `config.json` file in each example directory:

```json
{
    "api_url": "http://localhost:5000/api/v1",
    "api_key": "your-api-key-here",
    "webhook_url": "https://your-server.com/webhook",
    "timeout": 30,
    "retry_attempts": 3
}
```

## Testing

### Unit Tests

Each example includes basic unit tests:

**Python:**
```bash
python -m pytest test_cctv_client.py
```

**JavaScript:**
```bash
npm test
```

**PHP:**
```bash
phpunit CCTVAPIClientTest.php
```

### Integration Tests

Test against a running CCTV system:

```bash
# Start CCTV system
python app.py

# Run integration tests
python test_integration.py
```

## Performance Tips

1. **Connection Pooling** - Reuse HTTP connections for multiple requests
2. **Caching** - Cache frequently accessed data like system status
3. **Batch Operations** - Use batch endpoints when available
4. **Async Processing** - Use async/await for non-blocking operations
5. **Rate Limiting** - Respect API rate limits to avoid throttling

## Security Best Practices

1. **API Key Security** - Never expose API keys in client-side code
2. **HTTPS** - Always use HTTPS in production
3. **Input Validation** - Validate all user inputs
4. **Error Handling** - Don't expose sensitive information in error messages
5. **Logging** - Log API calls for debugging but avoid logging sensitive data

## Support

For help with the API examples:

1. Check the main API documentation
2. Review the example code comments
3. Test with the health check endpoint first
4. Verify your API key has the required permissions
5. Check the system logs for detailed error information

## Contributing

To contribute new examples or improvements:

1. Follow the existing code style and structure
2. Include comprehensive error handling
3. Add usage examples and documentation
4. Test with the actual CCTV system
5. Submit a pull request with your changes
