/**
 * CCTV Video Streaming Client - JavaScript/Node.js Example
 * =======================================================
 * 
 * This example demonstrates how to consume live video streams with detection overlay
 * from the CCTV Monitoring API using JavaScript.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

class CCTVVideoStreamClient {
    /**
     * Initialize the video streaming client
     * @param {string} baseUrl - Base URL of the CCTV API
     * @param {string} apiKey - API key (optional for public shared streams)
     */
    constructor(baseUrl, apiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.apiKey = apiKey;
        
        // Create axios instance
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 30000
        });
        
        if (apiKey) {
            this.client.defaults.headers['X-API-Key'] = apiKey;
        }
    }
    
    /**
     * Get stream information and capabilities
     * @param {string} streamId - Stream ID
     * @param {boolean} shared - Whether to use shared stream endpoint
     * @returns {Promise<Object>} Stream information
     */
    async getStreamInfo(streamId, shared = false) {
        try {
            const endpoint = shared ? `/shared/streams/${streamId}/info` : `/streams/${streamId}/stream_info`;
            const response = await this.client.get(endpoint);
            return response.data;
        } catch (error) {
            console.error('Error getting stream info:', error.message);
            return null;
        }
    }
    
    /**
     * Get current frame as base64 image
     * @param {string} streamId - Stream ID
     * @param {boolean} shared - Whether to use shared stream endpoint
     * @returns {Promise<Object>} Frame data
     */
    async getCurrentFrame(streamId, shared = false) {
        try {
            const endpoint = shared ? `/shared/streams/${streamId}/frame` : `/streams/${streamId}/frame`;
            const response = await this.client.get(endpoint);
            
            if (response.data.success) {
                return response.data.data;
            } else {
                console.error('Error:', response.data.message);
                return null;
            }
        } catch (error) {
            console.error('Error getting frame:', error.message);
            return null;
        }
    }
    
    /**
     * Save current frame to file
     * @param {string} streamId - Stream ID
     * @param {string} filename - Output filename
     * @param {boolean} shared - Whether to use shared stream endpoint
     */
    async saveCurrentFrame(streamId, filename, shared = false) {
        try {
            const frameData = await this.getCurrentFrame(streamId, shared);
            
            if (frameData && frameData.frame_base64) {
                const buffer = Buffer.from(frameData.frame_base64, 'base64');
                fs.writeFileSync(filename, buffer);
                console.log(`📸 Frame saved: ${filename}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error saving frame:', error.message);
            return false;
        }
    }
    
    /**
     * Get video stream URL
     * @param {string} streamId - Stream ID
     * @param {boolean} shared - Whether to use shared stream endpoint
     * @returns {string} Stream URL
     */
    getStreamUrl(streamId, shared = false) {
        const endpoint = shared ? `/shared/streams/${streamId}/video_feed` : `/streams/${streamId}/video_feed`;
        let url = `${this.baseUrl}${endpoint}`;
        
        // Add API key as query parameter for non-shared streams
        if (!shared && this.apiKey) {
            url += `?api_key=${this.apiKey}`;
        }
        
        return url;
    }
    
    /**
     * Monitor stream frames periodically
     * @param {string} streamId - Stream ID
     * @param {number} interval - Interval in milliseconds
     * @param {boolean} shared - Whether to use shared stream endpoint
     * @param {Function} callback - Callback function for each frame
     */
    async monitorFrames(streamId, interval = 5000, shared = false, callback = null) {
        console.log(`🔍 Monitoring stream ${streamId} every ${interval}ms`);
        
        const monitor = async () => {
            try {
                const frameData = await this.getCurrentFrame(streamId, shared);
                
                if (frameData) {
                    const timestamp = new Date().toISOString();
                    console.log(`📊 Frame ${frameData.frame_count} at ${timestamp}`);
                    
                    if (frameData.detections) {
                        console.log(`🚗 Detections: ${frameData.detections.length}`);
                        frameData.detections.forEach((detection, i) => {
                            console.log(`  ${i+1}. ${detection.vehicle_type} (${detection.confidence.toFixed(2)})`);
                        });
                    }
                    
                    // Call custom callback if provided
                    if (callback && typeof callback === 'function') {
                        callback(frameData);
                    }
                }
            } catch (error) {
                console.error('❌ Monitoring error:', error.message);
            }
        };
        
        // Initial call
        await monitor();
        
        // Set up interval
        const intervalId = setInterval(monitor, interval);
        
        // Return function to stop monitoring
        return () => {
            clearInterval(intervalId);
            console.log('⏹️ Monitoring stopped');
        };
    }
    
    /**
     * Create HTML page for viewing stream
     * @param {string} streamId - Stream ID
     * @param {boolean} shared - Whether to use shared stream endpoint
     * @param {string} outputFile - Output HTML filename
     */
    createStreamViewer(streamId, shared = false, outputFile = 'stream_viewer.html') {
        const streamUrl = this.getStreamUrl(streamId, shared);
        
        const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCTV Stream Viewer - ${streamId}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stream-container {
            text-align: center;
            margin-bottom: 20px;
        }
        .stream-video {
            max-width: 100%;
            border: 2px solid #333;
            border-radius: 5px;
        }
        .info-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 CCTV Stream Viewer</h1>
        <p><strong>Stream ID:</strong> ${streamId}</p>
        <p><strong>Access Type:</strong> ${shared ? 'Shared' : 'Private'}</p>
        
        <div class="stream-container">
            <img id="streamVideo" class="stream-video" src="${streamUrl}" alt="CCTV Stream">
        </div>
        
        <div class="controls">
            <button class="btn" onclick="refreshStream()">🔄 Refresh</button>
            <button class="btn" onclick="takeScreenshot()">📸 Screenshot</button>
            <button class="btn" onclick="toggleFullscreen()">🔍 Fullscreen</button>
        </div>
        
        <div id="status" class="status online">
            🟢 Stream Online
        </div>
        
        <div class="info-panel">
            <div class="info-card">
                <h3>📊 Stream Information</h3>
                <p id="streamInfo">Loading...</p>
            </div>
            <div class="info-card">
                <h3>🚗 Detection Stats</h3>
                <p id="detectionStats">Loading...</p>
            </div>
            <div class="info-card">
                <h3>⏱️ Current Time</h3>
                <p id="currentTime">Loading...</p>
            </div>
        </div>
    </div>

    <script>
        const streamVideo = document.getElementById('streamVideo');
        const status = document.getElementById('status');
        
        // Monitor stream status
        streamVideo.onload = () => {
            status.className = 'status online';
            status.innerHTML = '🟢 Stream Online';
        };
        
        streamVideo.onerror = () => {
            status.className = 'status offline';
            status.innerHTML = '🔴 Stream Offline';
        };
        
        // Refresh stream
        function refreshStream() {
            const currentSrc = streamVideo.src;
            streamVideo.src = '';
            setTimeout(() => {
                streamVideo.src = currentSrc + '?t=' + Date.now();
            }, 100);
        }
        
        // Take screenshot
        function takeScreenshot() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = streamVideo.naturalWidth || streamVideo.width;
            canvas.height = streamVideo.naturalHeight || streamVideo.height;
            
            ctx.drawImage(streamVideo, 0, 0, canvas.width, canvas.height);
            
            const link = document.createElement('a');
            link.download = 'cctv_screenshot_' + Date.now() + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Toggle fullscreen
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                streamVideo.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // Update current time
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }
        
        // Update info periodically
        async function updateInfo() {
            try {
                const response = await fetch('${this.baseUrl}${shared ? `/shared/streams/${streamId}/info` : `/streams/${streamId}/stream_info`}', {
                    headers: ${this.apiKey ? `{'X-API-Key': '${this.apiKey}'}` : '{}'}
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const info = data.data;
                        document.getElementById('streamInfo').innerHTML = 
                            'Active: ' + (info.is_active ? 'Yes' : 'No') + '<br>' +
                            'Capabilities: ' + Object.keys(info.capabilities || {}).join(', ');
                        
                        if (info.current_stats) {
                            document.getElementById('detectionStats').innerHTML = 
                                'Unique Vehicles: ' + (info.current_stats.unique_vehicles || 0) + '<br>' +
                                'Currently Tracked: ' + (info.current_stats.currently_tracked || 0);
                        }
                    }
                }
            } catch (error) {
                console.error('Error updating info:', error);
            }
        }
        
        // Initialize
        updateTime();
        updateInfo();
        
        // Update every 5 seconds
        setInterval(updateTime, 1000);
        setInterval(updateInfo, 5000);
        
        // Auto-refresh stream every 30 seconds to prevent timeout
        setInterval(refreshStream, 30000);
    </script>
</body>
</html>`;
        
        fs.writeFileSync(outputFile, html);
        console.log(`📄 Stream viewer created: ${outputFile}`);
        console.log(`🌐 Open in browser: file://${path.resolve(outputFile)}`);
    }
}

// Example usage functions

async function main() {
    console.log('🎥 CCTV Video Streaming Client - JavaScript');
    console.log('=' .repeat(50));
    
    // Initialize client
    const client = new CCTVVideoStreamClient(
        'http://localhost:5000/api/v1',
        'cctv_admin_Q-QTkUndp7CTecdIAs9NCwcpUKsYMPHZ0xUEhsgGuMk' // Replace with your API key
    );
    
    // Example stream ID (replace with actual)
    const streamId = 'your-stream-id-here';
    
    try {
        // Get stream info
        console.log('📋 Getting stream info...');
        const info = await client.getStreamInfo(streamId);
        if (info && info.success) {
            console.log('✅ Stream capabilities:', Object.keys(info.data.capabilities));
            console.log('📊 Current stats:', info.data.current_stats);
        }
        
        // Get current frame
        console.log('\n📸 Getting current frame...');
        const frameData = await client.getCurrentFrame(streamId);
        if (frameData) {
            console.log(`✅ Frame captured: ${frameData.frame_count}`);
            console.log(`🚗 Detections: ${frameData.detections?.length || 0}`);
        }
        
        // Save screenshot
        console.log('\n💾 Saving screenshot...');
        await client.saveCurrentFrame(streamId, `screenshot_${Date.now()}.jpg`);
        
        // Create HTML viewer
        console.log('\n📄 Creating HTML viewer...');
        client.createStreamViewer(streamId, false, 'cctv_viewer.html');
        
        // Monitor frames for 30 seconds
        console.log('\n🔍 Starting frame monitoring (30 seconds)...');
        const stopMonitoring = await client.monitorFrames(streamId, 3000, false, (frameData) => {
            // Custom callback for each frame
            if (frameData.detections && frameData.detections.length > 0) {
                console.log(`🚨 Alert: ${frameData.detections.length} vehicles detected!`);
            }
        });
        
        // Stop monitoring after 30 seconds
        setTimeout(() => {
            stopMonitoring();
            console.log('✅ Demo completed');
        }, 30000);
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

async function demoSharedStreams() {
    console.log('🌐 Demo: Accessing Shared Streams');
    console.log('=' .repeat(40));
    
    // Client without API key for public streams
    const client = new CCTVVideoStreamClient('http://localhost:5000/api/v1');
    
    try {
        // Get list of shared streams
        const response = await axios.get('http://localhost:5000/api/v1/shared/streams');
        
        if (response.data.success) {
            const publicStreams = response.data.data.public_streams;
            console.log(`📋 Found ${publicStreams.length} public shared streams`);
            
            if (publicStreams.length > 0) {
                const firstStream = publicStreams[0];
                console.log(`🎥 Accessing stream: ${firstStream.stream_id}`);
                
                // Create viewer for shared stream
                client.createStreamViewer(firstStream.stream_id, true, 'shared_stream_viewer.html');
                
                // Monitor shared stream
                const stopMonitoring = await client.monitorFrames(firstStream.stream_id, 5000, true);
                
                // Stop after 20 seconds
                setTimeout(stopMonitoring, 20000);
            }
        }
    } catch (error) {
        console.error('❌ Error accessing shared streams:', error.message);
    }
}

// Export for use as module
module.exports = {
    CCTVVideoStreamClient,
    main,
    demoSharedStreams
};

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}
