/* Custom CSS for CCTV Monitoring System */

/* General Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    background-color: #000;
    border-radius: 0;
    overflow: hidden;
}

.video-placeholder {
    width: 100%;
    height: 480px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.placeholder-content {
    text-align: center;
}

.video-stream {
    width: 100%;
    height: auto;
    max-height: 600px;
    object-fit: contain;
    background-color: #000;
}

.video-controls {
    border-top: 1px solid #dee2e6;
}

/* Statistics */
.stat-item {
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 10px;
}

.stat-item h3 {
    margin: 0;
    font-weight: bold;
}

.vehicle-stats {
    font-size: 0.9rem;
}

.vehicle-stats > div {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.vehicle-stats > div:last-child {
    border-bottom: none;
}

/* Detection Log */
.detection-log {
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

.log-entry {
    margin-bottom: 8px;
    padding: 5px 10px;
    border-left: 3px solid #007bff;
    background-color: white;
    border-radius: 3px;
}

.log-entry.vehicle-detected {
    border-left-color: #28a745;
}

.log-entry.error {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.log-entry.warning {
    border-left-color: #ffc107;
    background-color: #fff3cd;
}

/* Form Controls */
.form-range {
    cursor: pointer;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

/* Status Indicators */
.status-connected {
    color: #28a745;
    font-weight: bold;
}

.status-disconnected {
    color: #dc3545;
    font-weight: bold;
}

.status-connecting {
    color: #ffc107;
    font-weight: bold;
}

/* Loading Animation */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-placeholder {
        height: 300px;
    }
    
    .stat-item h3 {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .video-controls .row {
        text-align: center;
    }
    
    .video-controls .col-md-6:last-child {
        text-align: center !important;
        margin-top: 10px;
    }
}

/* Fullscreen Mode */
.fullscreen-video {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: #000;
    object-fit: contain;
}

/* Custom Scrollbar */
.detection-log::-webkit-scrollbar {
    width: 6px;
}

.detection-log::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.detection-log::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.detection-log::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Alert Customizations */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

.alert-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.alert-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #212529;
}

.alert-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

/* Input Group Styling */
.input-group .form-control {
    border-right: none;
}

.input-group .btn-outline-secondary {
    border-left: none;
    background-color: white;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Vehicle Type Icons */
.fas.fa-car { color: #28a745; }
.fas.fa-motorcycle { color: #dc3545; }
.fas.fa-bus { color: #007bff; }
.fas.fa-truck { color: #ffc107; }
.fas.fa-bicycle { color: #17a2b8; }
