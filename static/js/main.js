// Main JavaScript for CCTV Monitoring System

let streamActive = false;
let statsUpdateInterval = null;
let currentStreamId = null;

// Initialize stream monitoring functionality
function initializeStreamMonitoring() {
    setupEventListeners();
    updateUI();
}

// Setup all event listeners
function setupEventListeners() {
    // Stream control buttons
    $('#startStreamBtn').click(startStream);
    $('#stopStreamBtn').click(stopStream);
    $('#validateBtn').click(validateStreamUrl);
    $('#resetStatsBtn').click(resetStatistics);

    // Video controls
    $('#fullscreenBtn').click(toggleFullscreen);
    $('#screenshotBtn').click(takeScreenshot);

    // Settings controls
    $('#confidenceSlider').on('input', updateConfidenceValue);
    $('#strictFiltering').change(updateDetectionSettings);
    $('#motorcycleMode').change(updateDetectionSettings);

    // Stream URL input
    $('#streamUrl').on('keypress', function (e) {
        if (e.which === 13) { // Enter key
            startStream();
        }
    });

    // Auto-validate URL on input
    $('#streamUrl').on('input', debounce(validateStreamUrl, 1000));
}

// Start stream monitoring
function startStream() {
    const streamUrl = $('#streamUrl').val().trim();

    if (!streamUrl) {
        showError('Please enter a stream URL');
        return;
    }

    // Show loading modal
    $('#loadingModal').modal('show');

    // Update UI
    updateStreamStatus('Connecting...', 'connecting');
    $('#startStreamBtn').prop('disabled', true);

    // Send request to start stream
    $.ajax({
        url: '/start_stream',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ stream_url: streamUrl }),
        success: function (response) {
            $('#loadingModal').modal('hide');

            if (response.success) {
                streamActive = true;
                currentStreamId = response.stream_id;

                // Update UI
                updateStreamStatus('Connected', 'connected');
                $('#startStreamBtn').prop('disabled', true);
                $('#stopStreamBtn').prop('disabled', false);

                // Start video stream
                startVideoStream();

                // Start statistics updates
                startStatsUpdates();

                // Log success
                addLogEntry('Stream started successfully', 'success');

                showSuccess('Stream connected successfully!');
            } else {
                handleStreamError(response.error);
            }
        },
        error: function (xhr, status, error) {
            $('#loadingModal').modal('hide');
            handleStreamError('Failed to connect to server: ' + error);
        }
    });
}

// Stop stream monitoring
function stopStream() {
    $.ajax({
        url: '/stop_stream',
        method: 'POST',
        success: function (response) {
            if (response.success) {
                streamActive = false;
                currentStreamId = null;

                // Update UI
                updateStreamStatus('Disconnected', 'disconnected');
                $('#startStreamBtn').prop('disabled', false);
                $('#stopStreamBtn').prop('disabled', true);

                // Stop video stream
                stopVideoStream();

                // Stop statistics updates
                stopStatsUpdates();

                // Log success
                addLogEntry('Stream stopped', 'info');

                showSuccess('Stream stopped successfully!');
            } else {
                showError('Failed to stop stream: ' + response.error);
            }
        },
        error: function (xhr, status, error) {
            showError('Failed to stop stream: ' + error);
        }
    });
}

// Start video stream display
function startVideoStream() {
    const videoElement = $('#videoStream');
    const placeholder = $('#videoPlaceholder');

    // Set video source
    videoElement.attr('src', '/video_feed?' + new Date().getTime());

    // Show video, hide placeholder
    placeholder.hide();
    videoElement.show().addClass('fade-in');

    // Handle video load events
    videoElement.on('load', function () {
        updateConnectionStatus('Connected');
    });

    videoElement.on('error', function () {
        updateConnectionStatus('Error');
        addLogEntry('Video stream error', 'error');
    });
}

// Stop video stream display
function stopVideoStream() {
    const videoElement = $('#videoStream');
    const placeholder = $('#videoPlaceholder');

    // Hide video, show placeholder
    videoElement.hide().attr('src', '');
    placeholder.show().addClass('fade-in');

    updateConnectionStatus('Disconnected');
}

// Start statistics updates
function startStatsUpdates() {
    if (statsUpdateInterval) {
        clearInterval(statsUpdateInterval);
    }

    statsUpdateInterval = setInterval(updateStatistics, 2000); // Update every 2 seconds
}

// Stop statistics updates
function stopStatsUpdates() {
    if (statsUpdateInterval) {
        clearInterval(statsUpdateInterval);
        statsUpdateInterval = null;
    }
}

// Update statistics from server
function updateStatistics() {
    if (!streamActive) return;

    $.ajax({
        url: '/stream_stats',
        method: 'GET',
        success: function (response) {
            if (response.success) {
                const stats = response.stats;

                // Update main statistics
                $('#totalDetections').text(stats.total_detections || 0);
                $('#uniqueVehicles').text(stats.unique_vehicles || 0);
                $('#frameCount').text(stats.frame_count || 0);

                // Update vehicle breakdown
                const byType = stats.by_type || {};
                $('#carCount').text(byType['Mobil'] || 0);
                $('#motorcycleCount').text(byType['Motor'] || 0);
                $('#busCount').text(byType['Bus'] || 0);
                $('#truckCount').text(byType['Truk'] || 0);
                $('#bicycleCount').text(byType['Sepeda'] || 0);

                // Update connection status
                if (stats.is_active) {
                    updateConnectionStatus('Connected');
                } else {
                    updateConnectionStatus('Disconnected');
                }

                // Load auto-reset info if we have a stream ID
                if (currentStreamId) {
                    loadAutoResetInfo();
                }
            }
        },
        error: function () {
            updateConnectionStatus('Error');
        }
    });
}

// Validate stream URL
function validateStreamUrl() {
    const streamUrl = $('#streamUrl').val().trim();
    const validateBtn = $('#validateBtn');

    if (!streamUrl) {
        validateBtn.removeClass('btn-success btn-danger').addClass('btn-outline-secondary');
        validateBtn.html('<i class="fas fa-check"></i>');
        return;
    }

    // Simple URL validation
    try {
        new URL(streamUrl);
        validateBtn.removeClass('btn-outline-secondary btn-danger').addClass('btn-success');
        validateBtn.html('<i class="fas fa-check"></i>');
    } catch (e) {
        validateBtn.removeClass('btn-outline-secondary btn-success').addClass('btn-danger');
        validateBtn.html('<i class="fas fa-times"></i>');
    }
}

// Reset statistics
function resetStatistics() {
    if (confirm('Are you sure you want to reset all statistics?')) {
        // Reset UI counters
        $('#totalDetections').text('0');
        $('#uniqueVehicles').text('0');
        $('#carCount').text('0');
        $('#motorcycleCount').text('0');
        $('#busCount').text('0');
        $('#truckCount').text('0');
        $('#bicycleCount').text('0');

        // Clear detection log
        $('#detectionLog').html('<p class="text-muted">Detection events will appear here...</p>');

        addLogEntry('Statistics reset', 'info');
        showSuccess('Statistics reset successfully!');
    }
}

// Update confidence value display
function updateConfidenceValue() {
    const value = $('#confidenceSlider').val();
    $('#confidenceValue').text(value);
}

// Update detection settings
function updateDetectionSettings() {
    const strictFiltering = $('#strictFiltering').is(':checked');
    const motorcycleMode = $('#motorcycleMode').is(':checked');

    // Log settings change
    addLogEntry(`Settings updated: Strict=${strictFiltering}, Motorcycle=${motorcycleMode}`, 'info');
}

// Toggle fullscreen video
function toggleFullscreen() {
    const videoElement = document.getElementById('videoStream');

    if (!document.fullscreenElement) {
        videoElement.requestFullscreen().then(() => {
            videoElement.classList.add('fullscreen-video');
        });
    } else {
        document.exitFullscreen().then(() => {
            videoElement.classList.remove('fullscreen-video');
        });
    }
}

// Take screenshot
function takeScreenshot() {
    const videoElement = document.getElementById('videoStream');

    if (!videoElement.src) {
        showError('No active video stream to capture');
        return;
    }

    // Create canvas to capture frame
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = videoElement.naturalWidth || videoElement.width;
    canvas.height = videoElement.naturalHeight || videoElement.height;

    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

    // Download screenshot
    const link = document.createElement('a');
    link.download = `cctv_screenshot_${new Date().getTime()}.png`;
    link.href = canvas.toDataURL();
    link.click();

    addLogEntry('Screenshot captured', 'info');
    showSuccess('Screenshot saved!');
}

// Update stream status
function updateStreamStatus(message, type) {
    const statusElement = $('#streamStatus');
    const iconClass = type === 'connected' ? 'fa-check-circle' :
        type === 'connecting' ? 'fa-spinner fa-spin' : 'fa-info-circle';

    statusElement.removeClass('alert-info alert-success alert-warning alert-danger');

    if (type === 'connected') {
        statusElement.addClass('alert-success');
    } else if (type === 'connecting') {
        statusElement.addClass('alert-warning');
    } else {
        statusElement.addClass('alert-info');
    }

    statusElement.html(`<i class="fas ${iconClass}"></i> ${message}`);
}

// Update connection status
function updateConnectionStatus(status) {
    const statusElement = $('#connectionStatus');
    statusElement.removeClass('status-connected status-disconnected status-connecting');

    if (status === 'Connected') {
        statusElement.addClass('status-connected');
    } else if (status === 'Connecting') {
        statusElement.addClass('status-connecting');
    } else {
        statusElement.addClass('status-disconnected');
    }

    statusElement.text(status);
}

// Add entry to detection log
function addLogEntry(message, type = 'info') {
    const logContainer = $('#detectionLog');
    const timestamp = new Date().toLocaleTimeString();

    // Remove placeholder text if present
    if (logContainer.find('p.text-muted').length > 0) {
        logContainer.empty();
    }

    const logEntry = $(`
        <div class="log-entry ${type} slide-up">
            <strong>[${timestamp}]</strong> ${message}
        </div>
    `);

    logContainer.prepend(logEntry);

    // Keep only last 50 entries
    const entries = logContainer.find('.log-entry');
    if (entries.length > 50) {
        entries.slice(50).remove();
    }
}

// Handle stream errors
function handleStreamError(error) {
    streamActive = false;
    updateStreamStatus('Connection failed', 'error');
    $('#startStreamBtn').prop('disabled', false);
    $('#stopStreamBtn').prop('disabled', true);
    addLogEntry(`Stream error: ${error}`, 'error');
    showError(error);
}

// Show success message
function showSuccess(message) {
    // You can implement toast notifications here
    console.log('Success:', message);
}

// Show error message
function showError(message) {
    $('#errorMessage').text(message);
    $('#errorModal').modal('show');
    console.error('Error:', message);
}

// Update UI based on current state
function updateUI() {
    if (streamActive) {
        $('#startStreamBtn').prop('disabled', true);
        $('#stopStreamBtn').prop('disabled', false);
    } else {
        $('#startStreamBtn').prop('disabled', false);
        $('#stopStreamBtn').prop('disabled', true);
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Handle page unload
$(window).on('beforeunload', function () {
    if (streamActive) {
        stopStream();
    }
});

// ============================================================================
// AUTO-RESET FUNCTIONALITY
// ============================================================================

// Show auto-reset modal
function showAutoResetModal() {
    // Load current auto-reset settings
    loadAutoResetSettings();
    $('#autoResetModal').modal('show');
}

// Load auto-reset info for display (lightweight)
function loadAutoResetInfo() {
    if (!currentStreamId) return;

    $.ajax({
        url: `/api/v1/streams/${currentStreamId}/auto_reset`,
        method: 'GET',
        headers: {
            'X-API-Key': 'demo-key'
        },
        success: function (response) {
            if (response.success) {
                updateAutoResetDisplay(response.data.auto_reset_info);
            }
        },
        error: function (xhr) {
            // Silently fail for display updates
            console.log('Auto-reset info not available');
        }
    });
}

// Load current auto-reset settings
function loadAutoResetSettings() {
    if (!currentStreamId) {
        showError('No active stream to configure');
        return;
    }

    $.ajax({
        url: `/api/v1/streams/${currentStreamId}/auto_reset`,
        method: 'GET',
        headers: {
            'X-API-Key': 'demo-key' // In production, use proper API key management
        },
        success: function (response) {
            if (response.success) {
                const info = response.data.auto_reset_info;

                // Update modal form
                $('#autoResetEnabled').prop('checked', info.auto_reset_enabled);

                const [hour, minute] = info.reset_time.split(':');
                $('#resetHour').val(parseInt(hour));
                $('#resetMinute').val(parseInt(minute));

                // Update current status display
                $('#currentResetStatus').text(info.auto_reset_enabled ? 'Enabled' : 'Disabled');
                $('#currentResetTime').text(info.reset_time);
                $('#currentNextReset').text(info.next_reset);

                // Update main page display
                updateAutoResetDisplay(info);
            }
        },
        error: function (xhr) {
            console.error('Error loading auto-reset settings:', xhr);
            showError('Failed to load auto-reset settings');
        }
    });
}

// Update auto-reset display on main page
function updateAutoResetDisplay(info) {
    if (info.auto_reset_enabled) {
        $('#auto-reset-info').show();
        $('#reset-time').text(info.reset_time);
        $('#reset-status').removeClass('badge-secondary').addClass('badge-success').text('Aktif');
        $('#next-reset').text(info.next_reset);
    } else {
        $('#auto-reset-info').show();
        $('#reset-time').text(info.reset_time);
        $('#reset-status').removeClass('badge-success').addClass('badge-secondary').text('Nonaktif');
        $('#next-reset').text('-');
    }
}

// Save auto-reset settings
function saveAutoResetSettings() {
    if (!currentStreamId) {
        showError('No active stream to configure');
        return;
    }

    const settings = {
        enabled: $('#autoResetEnabled').is(':checked'),
        reset_hour: parseInt($('#resetHour').val()),
        reset_minute: parseInt($('#resetMinute').val())
    };

    // Validate input
    if (settings.reset_hour < 0 || settings.reset_hour > 23) {
        showError('Hour must be between 0 and 23');
        return;
    }

    if (settings.reset_minute < 0 || settings.reset_minute > 59) {
        showError('Minute must be between 0 and 59');
        return;
    }

    $.ajax({
        url: `/api/v1/streams/${currentStreamId}/auto_reset`,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-API-Key': 'demo-key' // In production, use proper API key management
        },
        data: JSON.stringify(settings),
        success: function (response) {
            if (response.success) {
                showSuccess('Auto-reset settings saved successfully');
                $('#autoResetModal').modal('hide');

                // Update display
                updateAutoResetDisplay(response.data.auto_reset_info);
            } else {
                showError(response.message || 'Failed to save settings');
            }
        },
        error: function (xhr) {
            console.error('Error saving auto-reset settings:', xhr);
            const response = xhr.responseJSON;
            showError(response?.message || 'Failed to save auto-reset settings');
        }
    });
}

// Perform manual reset
function performManualReset() {
    if (!currentStreamId) {
        showError('No active stream to reset');
        return;
    }

    if (!confirm('Are you sure you want to reset all statistics? This action cannot be undone.')) {
        return;
    }

    $.ajax({
        url: `/api/v1/streams/${currentStreamId}/manual_reset`,
        method: 'POST',
        headers: {
            'X-API-Key': 'demo-key' // In production, use proper API key management
        },
        success: function (response) {
            if (response.success) {
                showSuccess('Statistics reset successfully');
                $('#autoResetModal').modal('hide');

                // Update statistics display
                updateStatistics();
            } else {
                showError(response.message || 'Failed to reset statistics');
            }
        },
        error: function (xhr) {
            console.error('Error performing manual reset:', xhr);
            const response = xhr.responseJSON;
            showError(response?.message || 'Failed to reset statistics');
        }
    });
}

// Reset statistics (legacy function - now calls manual reset)
function resetStatistics() {
    if (!streamActive) {
        showError('No active stream to reset');
        return;
    }

    if (!confirm('Are you sure you want to reset all statistics? This action cannot be undone.')) {
        return;
    }

    $.ajax({
        url: '/reset_stats',
        method: 'POST',
        success: function (response) {
            if (response.success) {
                showSuccess('Statistics reset successfully');
                updateStatistics();
            } else {
                showError(response.message || 'Failed to reset statistics');
            }
        },
        error: function (xhr) {
            console.error('Error resetting statistics:', xhr);
            showError('Failed to reset statistics');
        }
    });
}

// Show success message
function showSuccess(message) {
    // Create and show success toast/alert
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Add to top of page
    $('body').prepend(alertHtml);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        $('.alert-success').fadeOut();
    }, 5000);
}
