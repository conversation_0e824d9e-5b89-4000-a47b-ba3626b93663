# CCTV Monitoring System - API Documentation

## Overview

The CCTV Monitoring System provides a comprehensive REST API that allows external applications to monitor CCTV streams, access detection data, and integrate with the vehicle detection system programmatically.

## Base URL

```
http://localhost:5000/api/v1
```

## Authentication

All API endpoints (except health check) require authentication using API keys.

### API Key Authentication

Include your API key in the request header:

```http
X-API-Key: your-api-key-here
```

Or as a query parameter:

```http
GET /api/v1/streams?api_key=your-api-key-here
```

### Getting API Keys

Contact the system administrator to obtain API keys, or use the admin endpoints to generate new keys.

## Rate Limiting

API requests are rate-limited based on your API key:

- **Read operations**: 300 requests per hour
- **Write operations**: 100 requests per hour
- **Admin operations**: 50 requests per hour

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 300
X-RateLimit-Remaining: 299
X-RateLimit-Reset: **********
```

## Response Format

All API responses follow a consistent JSON format:

### Success Response

```json
{
    "success": true,
    "data": {
        // Response data here
    },
    "message": "Operation completed successfully",
    "timestamp": "2024-08-01T10:30:00Z"
}
```

### Error Response

```json
{
    "success": false,
    "error": "Error type",
    "message": "Detailed error message",
    "timestamp": "2024-08-01T10:30:00Z"
}
```

## API Endpoints

### 1. Stream Management

#### List All Streams

```http
GET /api/v1/streams
```

**Response:**

```json
{
    "success": true,
    "data": {
        "streams": [
            {
                "stream_id": "uuid-here",
                "stream_url": "http://example.com/stream.m3u8",
                "is_active": true,
                "frame_count": 1500,
                "detection_stats": {
                    "total_detections": 45,
                    "unique_vehicles": 12,
                    "by_type": {
                        "Mobil": 8,
                        "Motor": 4
                    }
                }
            }
        ],
        "total_count": 1
    }
}
```

#### Create New Stream

```http
POST /api/v1/streams
```

**Request Body:**

```json
{
    "stream_url": "http://example.com/stream.m3u8",
    "name": "Main Entrance Camera"
}
```

**Response:**

```json
{
    "success": true,
    "data": {
        "stream_id": "uuid-here",
        "stream_url": "http://example.com/stream.m3u8",
        "name": "Main Entrance Camera",
        "status": "active"
    },
    "message": "Stream created and started successfully"
}
```

#### Get Stream Details

```http
GET /api/v1/streams/{stream_id}
```

**Response:**

```json
{
    "success": true,
    "data": {
        "stream_id": "uuid-here",
        "stream_url": "http://example.com/stream.m3u8",
        "is_active": true,
        "frame_count": 1500,
        "detection_stats": {
            "total_detections": 45,
            "unique_vehicles": 12,
            "by_type": {
                "Mobil": 8,
                "Motor": 4
            }
        },
        "active_vehicles": 3,
        "tracking_stats": {
            "total_unique": 12,
            "currently_tracked": 3,
            "by_type": {
                "Mobil": 8,
                "Motor": 4
            }
        }
    }
}
```

#### Delete Stream

```http
DELETE /api/v1/streams/{stream_id}
```

**Response:**

```json
{
    "success": true,
    "message": "Stream uuid-here stopped and deleted successfully"
}
```

### 2. Detection Data

#### Get Current Detections

```http
GET /api/v1/streams/{stream_id}/detections
```

**Response:**

```json
{
    "success": true,
    "data": {
        "stream_id": "uuid-here",
        "detections": [
            {
                "vehicle_id": 1,
                "class_id": 2,
                "confidence": 0.85,
                "bbox": {
                    "x1": 100.5,
                    "y1": 200.3,
                    "x2": 300.7,
                    "y2": 400.9
                },
                "center": {
                    "x": 200.6,
                    "y": 300.6
                },
                "frames_seen": 15,
                "counted_as_unique": true
            }
        ],
        "detection_count": 1,
        "frame_count": 1500
    }
}
```

#### Get Stream Statistics

```http
GET /api/v1/streams/{stream_id}/statistics
```

**Response:**

```json
{
    "success": true,
    "data": {
        "stream_id": "uuid-here",
        "frame_count": 1500,
        "detection_stats": {
            "total_detections": 45,
            "unique_vehicles": 12,
            "by_type": {
                "Mobil": 8,
                "Motor": 4
            }
        },
        "tracking_stats": {
            "total_unique": 12,
            "currently_tracked": 3,
            "by_type": {
                "Mobil": 8,
                "Motor": 4
            }
        },
        "is_active": true
    }
}
```

#### Reset Stream Statistics

```http
DELETE /api/v1/streams/{stream_id}/statistics
```

**Response:**

```json
{
    "success": true,
    "message": "Statistics reset for stream uuid-here"
}
```

### 3. System Monitoring

#### Get System Status

```http
GET /api/v1/system/status
```

**Response:**

```json
{
    "success": true,
    "data": {
        "system": {
            "status": "healthy",
            "uptime": **********,
            "version": "1.0.0"
        },
        "streams": {
            "total": 2,
            "active": 1,
            "inactive": 1
        },
        "detections": {
            "total_detections": 150,
            "total_unique_vehicles": 45
        },
        "resources": {
            "cpu_percent": 25.5,
            "memory": {
                "total": **********,
                "available": **********,
                "percent": 50.0
            },
            "disk": {
                "total": 1000000000000,
                "free": ************,
                "percent": 50.0
            },
            "gpu": {
                "available": true,
                "device_count": 1,
                "current_device": 0,
                "device_name": "NVIDIA GeForce RTX 3080",
                "memory_allocated": **********,
                "memory_reserved": **********
            }
        }
    }
}
```

#### Health Check

```http
GET /api/v1/system/health
```

**Response:**

```json
{
    "status": "healthy",
    "timestamp": "2024-08-01T10:30:00Z",
    "version": "1.0.0"
}
```

### 4. API Key Management (Admin Only)

#### List API Keys

```http
GET /api/v1/auth/keys
```

**Response:**

```json
{
    "success": true,
    "data": {
        "api_keys": [
            {
                "api_key": "cctv_admin_...",
                "name": "Admin Key",
                "permissions": ["read", "write", "admin"],
                "created_at": "2024-08-01T10:00:00Z",
                "last_used": "2024-08-01T10:30:00Z",
                "usage_count": 25
            }
        ],
        "total_count": 1
    }
}
```

#### Create API Key

```http
POST /api/v1/auth/keys
```

**Request Body:**

```json
{
    "name": "Integration Key",
    "permissions": ["read", "write"]
}
```

**Response:**

```json
{
    "success": true,
    "data": {
        "api_key": "cctv_integration_abc123...",
        "name": "Integration Key",
        "permissions": ["read", "write"]
    },
    "message": "API key created successfully"
}
```

### 5. Webhooks

#### List Webhooks

```http
GET /api/v1/webhooks
```

**Response:**

```json
{
    "success": true,
    "data": {
        "webhooks": [
            {
                "webhook_id": "uuid-here",
                "url": "https://your-server.com/webhook",
                "events": ["vehicle_detected", "unique_vehicle_counted"],
                "active": true,
                "created_at": "2024-08-01T10:00:00Z"
            }
        ],
        "total_count": 1
    }
}
```

#### Register Webhook

```http
POST /api/v1/webhooks
```

**Request Body:**

```json
{
    "url": "https://your-server.com/webhook",
    "events": ["vehicle_detected", "unique_vehicle_counted", "stream_started"]
}
```

**Response:**

```json
{
    "success": true,
    "data": {
        "webhook_id": "uuid-here",
        "url": "https://your-server.com/webhook",
        "events": ["vehicle_detected", "unique_vehicle_counted", "stream_started"]
    },
    "message": "Webhook registered successfully"
}
```

#### Delete Webhook

```http
DELETE /api/v1/webhooks/{webhook_id}
```

**Response:**

```json
{
    "success": true,
    "message": "Webhook uuid-here deleted successfully"
}
```

## Webhook Events

When events occur, the system will send POST requests to registered webhook URLs with the following format:

### Vehicle Detected Event

```json
{
    "event_type": "vehicle_detected",
    "timestamp": "2024-08-01T10:30:00Z",
    "data": {
        "stream_id": "uuid-here",
        "vehicle_id": 1,
        "class_id": 2,
        "vehicle_type": "Mobil",
        "confidence": 0.85,
        "bbox": {
            "x1": 100.5,
            "y1": 200.3,
            "x2": 300.7,
            "y2": 400.9
        }
    }
}
```

### Unique Vehicle Counted Event

```json
{
    "event_type": "unique_vehicle_counted",
    "timestamp": "2024-08-01T10:30:00Z",
    "data": {
        "stream_id": "uuid-here",
        "vehicle_id": 1,
        "vehicle_type": "Mobil",
        "total_unique_count": 13
    }
}
```

### Stream Started Event

```json
{
    "event_type": "stream_started",
    "timestamp": "2024-08-01T10:30:00Z",
    "data": {
        "stream_id": "uuid-here",
        "stream_url": "http://example.com/stream.m3u8"
    }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - API key required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

## Vehicle Classes

| Class ID | Vehicle Type | Description |
|----------|--------------|-------------|
| 1 | Sepeda | Bicycle |
| 2 | Mobil | Car |
| 3 | Motor | Motorcycle |
| 5 | Bus | Bus |
| 7 | Truk | Truck |

## Permissions

| Permission | Description |
|------------|-------------|
| read | Can view streams, detections, and statistics |
| write | Can create/delete streams, reset statistics, manage webhooks |
| admin | Can manage API keys and system settings |

## Best Practices

1. **Store API keys securely** - Never expose API keys in client-side code
2. **Use HTTPS** - Always use HTTPS in production environments
3. **Handle rate limits** - Implement exponential backoff for rate-limited requests
4. **Monitor webhook delivery** - Implement proper error handling for webhook endpoints
5. **Cache responses** - Cache frequently accessed data to reduce API calls
6. **Use appropriate permissions** - Use the minimum required permissions for each API key

## SDKs and Examples

See the `examples/` directory for code samples in various programming languages:

- Python
- JavaScript/Node.js
- PHP
- Java
- C#

## Support

For API support and questions:

- Check the system logs for detailed error information
- Review the rate limiting headers in responses
- Ensure your API key has the required permissions
- Verify the request format matches the documentation
