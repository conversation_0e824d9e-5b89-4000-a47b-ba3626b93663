# Auto-Reset Feature - Complete Guide

## 🕛 Overview

Sistem CCTV Monitoring sekarang dilengkapi dengan **fitur auto-reset** yang secara otomatis mereset counting kendaraan setiap hari pada jam yang ditentukan (default: 00:00 / 12 malam).

## ✅ Fitur Auto-Reset

### **🔄 Automatic Daily Reset**
- **Scheduled Reset** - Otomatis reset setiap hari pada jam yang ditentukan
- **Configurable Time** - Dapat mengatur jam dan menit reset (default: 00:00)
- **Thread-Safe** - Menggunakan threading yang aman untuk background processing
- **Logging** - Mencatat aktivitas reset dengan detail statistik sebelumnya

### **⚙️ Manual Control**
- **Manual Reset** - Reset manual kapan saja melalui web interface atau API
- **Enable/Disable** - Dapat mengaktifkan atau menonaktifkan auto-reset
- **Real-time Configuration** - Pengaturan dapat diubah tanpa restart sistem

### **📊 Statistics Preservation**
- **Pre-Reset Logging** - Statistik sebelum reset dicatat di log
- **Webhook Notifications** - Notifikasi webhook saat reset terjadi
- **Historical Data** - Data sebelum reset tersimpan untuk analisis

## 🎯 Cara Menggunakan

### **1. Web Interface**

#### **Melihat Status Auto-Reset:**
- Buka web interface di `http://localhost:5000`
- Start stream CCTV
- Lihat panel "Detection Statistics"
- Informasi auto-reset akan muncul di bawah statistik

#### **Mengatur Auto-Reset:**
1. Klik tombol **🕛** (clock icon) di panel statistik
2. Modal "Auto-Reset Settings" akan terbuka
3. Konfigurasi pengaturan:
   - **Enable/Disable**: Toggle auto-reset on/off
   - **Hour**: Jam reset (0-23)
   - **Minute**: Menit reset (0-59)
4. Klik "Save Settings" untuk menyimpan

#### **Manual Reset:**
1. Klik tombol **🔄** (refresh icon) di panel statistik, atau
2. Buka modal auto-reset dan klik "Reset Now"
3. Konfirmasi reset (tidak dapat dibatalkan)

### **2. API Integration**

#### **Get Auto-Reset Info:**
```bash
GET /api/v1/streams/{stream_id}/auto_reset
X-API-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stream_id": "abc123",
    "auto_reset_info": {
      "auto_reset_enabled": true,
      "reset_time": "00:00",
      "last_reset_date": "2024-08-01",
      "next_reset": "2024-08-02 00:00"
    }
  }
}
```

#### **Configure Auto-Reset:**
```bash
POST /api/v1/streams/{stream_id}/auto_reset
X-API-Key: your-api-key
Content-Type: application/json

{
  "enabled": true,
  "reset_hour": 0,
  "reset_minute": 0
}
```

#### **Manual Reset:**
```bash
POST /api/v1/streams/{stream_id}/manual_reset
X-API-Key: your-api-key
```

#### **Global Auto-Reset (All Streams):**
```bash
# Get all streams auto-reset info
GET /api/v1/system/auto_reset

# Configure all streams
POST /api/v1/system/auto_reset
{
  "enabled": true,
  "reset_hour": 0,
  "reset_minute": 0
}

# Manual reset all streams
POST /api/v1/system/manual_reset_all
```

### **3. Python Client Example**

```python
from examples.python.cctv_api_client import CCTVAPIClient

client = CCTVAPIClient('http://localhost:5000/api/v1', 'your-api-key')

# Get auto-reset info
response = client.session.get(f"{client.base_url}/streams/{stream_id}/auto_reset")
auto_reset_info = response.json()['data']['auto_reset_info']

print(f"Auto-reset enabled: {auto_reset_info['auto_reset_enabled']}")
print(f"Reset time: {auto_reset_info['reset_time']}")
print(f"Next reset: {auto_reset_info['next_reset']}")

# Configure auto-reset
config_data = {
    "enabled": True,
    "reset_hour": 6,  # Reset at 6 AM
    "reset_minute": 0
}

response = client.session.post(
    f"{client.base_url}/streams/{stream_id}/auto_reset",
    json=config_data
)

if response.json()['success']:
    print("Auto-reset configured successfully")

# Manual reset
response = client.session.post(f"{client.base_url}/streams/{stream_id}/manual_reset")
if response.json()['success']:
    print("Manual reset performed successfully")
```

### **4. JavaScript Example**

```javascript
// Get auto-reset info
async function getAutoResetInfo(streamId) {
    const response = await fetch(`/api/v1/streams/${streamId}/auto_reset`, {
        headers: { 'X-API-Key': 'your-api-key' }
    });
    
    const data = await response.json();
    if (data.success) {
        console.log('Auto-reset info:', data.data.auto_reset_info);
        return data.data.auto_reset_info;
    }
}

// Configure auto-reset
async function configureAutoReset(streamId, enabled, hour, minute) {
    const response = await fetch(`/api/v1/streams/${streamId}/auto_reset`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-API-Key': 'your-api-key'
        },
        body: JSON.stringify({
            enabled: enabled,
            reset_hour: hour,
            reset_minute: minute
        })
    });
    
    const data = await response.json();
    return data.success;
}

// Manual reset
async function performManualReset(streamId) {
    const response = await fetch(`/api/v1/streams/${streamId}/manual_reset`, {
        method: 'POST',
        headers: { 'X-API-Key': 'your-api-key' }
    });
    
    const data = await response.json();
    return data.success;
}
```

## 🔔 Webhook Notifications

Auto-reset akan mengirim webhook notifications untuk events berikut:

### **Daily Reset Event:**
```json
{
  "event_type": "daily_reset",
  "timestamp": "2024-08-02T00:00:00Z",
  "data": {
    "reset_time": "2024-08-02T00:00:00Z",
    "previous_day_stats": {
      "total_unique": 150,
      "by_type": {
        "2": 80,  // Mobil
        "3": 70   // Motor
      },
      "date": "2024-08-01"
    },
    "new_date": "2024-08-02"
  }
}
```

### **Manual Reset Event:**
```json
{
  "event_type": "manual_reset",
  "timestamp": "2024-08-01T15:30:00Z",
  "data": {
    "stream_id": "abc123",
    "reset_time": "2024-08-01T15:30:00Z",
    "reset_type": "manual"
  }
}
```

## ⚙️ Configuration Options

### **Reset Time Settings:**
- **Hour**: 0-23 (24-hour format)
- **Minute**: 0-59
- **Default**: 00:00 (midnight)

### **Auto-Reset Control:**
- **Enable/Disable**: Toggle auto-reset functionality
- **Per-Stream**: Each stream can have different settings
- **Global Control**: Configure all streams at once

### **Thread Safety:**
- **Background Thread**: Auto-reset berjalan di background thread
- **Thread Lock**: Menggunakan threading lock untuk safety
- **Non-blocking**: Tidak mengganggu operasi deteksi

## 📊 Use Cases

### **1. Daily Traffic Reports:**
```python
# Set reset at 6 AM for daily reports
client.session.post(f"/api/v1/streams/{stream_id}/auto_reset", json={
    "enabled": True,
    "reset_hour": 6,
    "reset_minute": 0
})

# Generate daily report before reset
def generate_daily_report():
    stats = client.get_statistics(stream_id)
    report = {
        'date': datetime.now().date(),
        'total_vehicles': stats['tracking_stats']['total_unique'],
        'by_type': stats['tracking_stats']['by_type']
    }
    save_report(report)
```

### **2. Shift-based Monitoring:**
```python
# Reset every 8 hours for 3-shift monitoring
shifts = [
    {"hour": 6, "minute": 0},   # Morning shift
    {"hour": 14, "minute": 0},  # Afternoon shift  
    {"hour": 22, "minute": 0}   # Night shift
]

# Configure different streams for different shifts
for i, shift in enumerate(shifts):
    stream_id = shift_streams[i]
    client.session.post(f"/api/v1/streams/{stream_id}/auto_reset", json={
        "enabled": True,
        "reset_hour": shift["hour"],
        "reset_minute": shift["minute"]
    })
```

### **3. Weekly/Monthly Reports:**
```python
# Disable auto-reset for longer period analysis
client.session.post(f"/api/v1/streams/{stream_id}/auto_reset", json={
    "enabled": False
})

# Manual reset when needed
def weekly_reset():
    # Generate weekly report first
    generate_weekly_report()
    
    # Then reset for new week
    client.session.post(f"/api/v1/streams/{stream_id}/manual_reset")
```

## 🔧 Technical Implementation

### **Background Thread:**
- Runs every 30 seconds to check reset time
- Thread-safe with proper locking mechanisms
- Automatic error handling and recovery

### **Reset Process:**
1. **Check Time**: Compare current time with configured reset time
2. **Lock Resources**: Acquire thread lock for safety
3. **Save Statistics**: Log current statistics before reset
4. **Reset Counters**: Clear all tracking data and counters
5. **Update Date**: Update last reset date
6. **Send Notifications**: Send webhook notifications
7. **Release Lock**: Release thread lock

### **Data Preservation:**
- Statistics before reset are logged to console
- Webhook notifications include previous day data
- Historical data can be captured via webhooks

## 🎉 Benefits

✅ **Automated Management** - No manual intervention needed  
✅ **Flexible Scheduling** - Configurable reset time  
✅ **Data Integrity** - Thread-safe operations  
✅ **Historical Tracking** - Previous data logged and notified  
✅ **Real-time Control** - Can be configured without restart  
✅ **Multi-stream Support** - Individual or global configuration  
✅ **API Integration** - Full API support for automation  

**Sistem CCTV Monitoring sekarang dapat melakukan reset otomatis untuk analisis harian, shift-based monitoring, dan reporting yang lebih terstruktur!**
